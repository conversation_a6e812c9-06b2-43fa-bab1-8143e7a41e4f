_config_path: config.yaml
augmentation:
  cddpm:
    beta_end: 0.02
    beta_schedule: linear
    beta_start: 0.0001
    guidance_scale: 1.0
    timesteps: 1000
    unconditional_prob: 0.1
  cgan:
    beta1: 0.5
    discriminator_lr: 0.0002
    epochs: 100
    generator_lr: 0.0002
    latent_dim: 100
  classifier_healthy_samples:
    real_healthy_count: -1
    use_real_when_no_generated: true
  cvae:
    epochs: 100
    hidden_dim: 512
    latent_dim: 100
    learning_rate: 0.0001
  dcgan:
    discriminator_lr: 0.0002
    epochs: 100
    generator_lr: 0.0002
    latent_dim: 100
  ddpm:
    beta_schedule: linear
    epochs: 100
    learning_rate: 0.0001
    timesteps: 1000
  generate_fault_only: false
  generation_strategy:
    adaptive_generation: true
    initial_multiplier: 5.0
    max_multiplier: 10.0
    min_multiplier: 2.0
    target_samples_per_class: -1
  method: CDDPM
  num_generated_per_class: 5
  save_generated: true
  traditional:
    adasyn:
      n_neighbors: 5
      sampling_strategy: auto
    kmeans_smote:
      k_neighbors: 5
      sampling_strategy: auto
    rwo_sampling:
      sampling_strategy: auto
    smoteenn:
      sampling_strategy: auto
  wgan:
    clip_value: 0.01
    critic_lr: 5.0e-05
    epochs: 100
    generator_lr: 5.0e-05
    latent_dim: 100
    n_critic: 5
  wgan_gp:
    critic_lr: 0.0001
    epochs: 100
    generator_lr: 0.0001
    lambda_gp: 10
    latent_dim: 100
    n_critic: 5
data_screening:
  confidence_filter:
    adaptive: true
    enabled: true
    max_threshold: 0.8
    min_threshold: 0.1
    per_class: false
    threshold: 0.3
  diversity_selection:
    enabled: false
    method: kmeans
    n_clusters: 10
  enabled: false
  influence_filter:
    adaptive: true
    enabled: false
    method: tracin
    ratio: 0.3
  outlier_detection:
    contamination: 0.1
    enabled: false
    k_neighbors: 5
    method: lof
  screening_level: basic
  target_control:
    enabled: true
    fallback_strategy: relax_thresholds
    strict_target: false
dataset:
  data_loading:
    data_type: sequential
    fault_samples:
      max_fault_samples_per_class: 3
    healthy_samples:
      healthy_label: 0
      max_healthy_samples: -1
    normalization_method: minmax
    normalize: true
    original_length: 1024
    sample_selection: sequential
    signal_length: 1024
    train_val_split: 0.7
  datasets:
    JST:
      class_names:
      - '0'
      - '1'
      - '2'
      num_classes: 3
    KAT:
      class_names:
      - K001
      - KA01
      - KA05
      - KA09
      - KI01
      - KI03
      - KI05
      - KI08
      num_classes: 8
    SK:
      class_names:
      - '0'
      - '1'
      - '2'
      num_classes: 3
  name: KAT
evaluation:
  gan_evaluation:
    batch_size: 64
    classifier_epochs: 300
  metrics:
    classification:
    - accuracy
    - precision
    - recall
    - f1_score
    generation:
    - gan_train
    - gan_test
  visualization:
    num_samples_to_plot: 10
    save_confusion_matrix: true
    save_sample_plots: true
    save_tsne: true
    tsne_n_iter: 1000
    tsne_perplexity: 30
experiment:
  current_experiment:
    index: 1
    parameters:
      augmentation.num_generated_per_class: 5
    timestamp: '20250626_170105'
    total: 2
  description: 基于CDDPM的一维振动信号故障诊断数据增强
  name: cddpm_fault_diagnosis
  results:
    create_timestamp_folder: true
    save_comparison_csv: true
    save_individual: true
    save_plots_csv: true
  tags:
  - CDDPM
  - fault_diagnosis
  - vibration_signal
models:
  diffusion:
    dropout: 0.1
    encoder_channels:
    - 64
    - 128
    - 256
    - 512
    - 1024
    hidden_dim: 64
    in_channels: 1
    num_layers_per_block: 2
    out_channels: 1
    use_skip_connections: true
  mrcnn:
    base_channels: 64
    dropout: 0.1
    input_channels: 1
    num_blocks: 4
    num_classes: 8
    use_attention: false
performance_profiles:
  fast:
    system:
      num_workers: 0
      optimization:
        benchmark: true
        channels_last: false
        compile_model: false
        use_amp: false
      pin_memory: false
    training:
      classifier:
        batch_size: 32
        epochs: 2
      diffusion:
        batch_size: 32
        epochs: 2
  high_performance:
    system:
      num_workers: 0
      optimization:
        benchmark: true
        channels_last: true
        compile_model: true
        use_amp: true
      pin_memory: true
    training:
      classifier:
        batch_size: 128
      diffusion:
        batch_size: 128
  standard:
    system:
      num_workers: 0
      optimization:
        benchmark: true
        channels_last: false
        compile_model: false
        use_amp: false
      pin_memory: false
    training:
      classifier:
        batch_size: 64
      diffusion:
        batch_size: 64
  ultra:
    system:
      num_workers: 0
      optimization:
        benchmark: true
        channels_last: true
        compile_model: true
        use_amp: true
      pin_memory: true
    training:
      classifier:
        batch_size: 256
      diffusion:
        batch_size: 256
system:
  device: auto
  num_workers: 0
  optimization:
    benchmark: true
    channels_last: true
    compile_model: true
    use_amp: true
  performance_mode: high_performance
  pin_memory: false
  save:
    checkpoints_dir: checkpoints
    combined_datasets_dir: combined_datasets
    generated_samples_dir: generated_samples/{dataset_name}
    logs_dir: logs
    max_checkpoints_to_keep: 3
    results_dir: results
    save_best_only: true
    save_every_n_epochs: 1000
  seed: 42
training:
  classifier:
    batch_size: 128
    best_model_criteria:
      metric: weighted_loss
      mode: min
      weighted_loss:
        train_weight: 0.7
        val_weight: 0.3
    early_stopping:
      enabled: false
      min_delta: 0.001
      mode: min
      monitor: weighted_loss
      patience: 30
      restore_best_weights: true
      weighted_loss:
        train_weight: 0.7
        val_weight: 0.3
    epochs: 10
    learning_rate: 0.0001
    scheduler:
      T_max: 300
      eta_min: 1.0e-05
      type: cosine
    weight_decay: 0.0001
  diffusion:
    batch_size: 128
    best_model_criteria:
      metric: weighted_loss
      mode: min
      weighted_loss:
        train_weight: 0.7
        val_weight: 0.3
    early_stopping:
      enabled: false
      min_delta: 0.0001
      mode: min
      monitor: weighted_loss
      patience: 1000
      restore_best_weights: true
      weighted_loss:
        train_weight: 0.7
        val_weight: 0.3
    epochs: 20
    learning_rate: 0.0001
    loss_function: MAE
    optimizer: Adam
    scheduler:
      T_max: 20000
      eta_min: 1.0e-06
      gamma: 0.5
      step_size: 2000
      type: cosine
    weight_decay: 0.0001
