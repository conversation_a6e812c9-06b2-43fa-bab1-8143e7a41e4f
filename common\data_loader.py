"""
数据加载器模块
支持一维振动信号数据的加载、预处理和增强
"""

import os
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler
from sklearn.model_selection import train_test_split
import random
from typing import Tuple, Optional, Dict, List
import logging

logger = logging.getLogger(__name__)


class VibrationDataset(Dataset):
    """一维振动信号数据集类"""
    
    def __init__(self, data: np.ndarray, labels: np.ndarray, transform=None):
        """
        初始化数据集
        
        Args:
            data: 振动信号数据，形状为 (N, signal_length)
            labels: 标签数据，形状为 (N,)
            transform: 数据变换函数
        """
        self.data = torch.FloatTensor(data)
        self.labels = torch.LongTensor(labels.flatten())
        self.transform = transform
        
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        signal = self.data[idx]
        label = self.labels[idx]
        
        if self.transform:
            signal = self.transform(signal)
            
        return signal, label


class VibrationDataLoader:
    """振动信号数据加载器"""
    
    def __init__(self, config: Dict):
        """
        初始化数据加载器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.dataset_name = config['dataset']['name']
        self.data_config = config['dataset']['data_loading']
        
        # 数据路径 - 根据数据类型选择
        self.data_type = self.data_config.get('data_type', 'random')  # 默认使用随机数据
        self.sample_selection = self.data_config.get('sample_selection', 'sequential')  # 默认顺序选择

        if self.data_type == 'sequential':
            self.dataset_path = f"dataset/{self.dataset_name}/used_npy_sequential"
            logger.info(f"使用顺序采样数据: {self.dataset_path}")
        else:
            self.dataset_path = f"dataset/{self.dataset_name}/used_npy"
            logger.info(f"使用随机采样数据: {self.dataset_path}")

        # 验证配置一致性
        if self.data_type == 'sequential' and self.sample_selection == 'random':
            logger.warning("配置不一致：使用顺序采样数据但样本选择方式为随机，建议保持一致")
        elif self.data_type == 'random' and self.sample_selection == 'sequential':
            logger.warning("配置不一致：使用随机采样数据但样本选择方式为顺序，建议保持一致")

        logger.info(f"数据类型: {self.data_type}, 样本选择方式: {self.sample_selection}")
        
        # 归一化器
        self.scaler = None
        self._init_scaler()
        
    def _init_scaler(self):
        """初始化数据归一化器"""
        if not self.data_config['normalize']:
            return
            
        method = self.data_config['normalization_method']
        if method == 'minmax':
            self.scaler = MinMaxScaler()
        elif method == 'zscore':
            self.scaler = StandardScaler()
        elif method == 'robust':
            self.scaler = RobustScaler()
        else:
            raise ValueError(f"不支持的归一化方法: {method}")
    
    def _load_raw_data(self, split: str) -> Tuple[np.ndarray, np.ndarray]:
        """
        加载原始数据

        Args:
            split: 数据分割类型 ('train' 或 'test')

        Returns:
            data: 信号数据
            labels: 标签数据
        """
        # 根据数据类型确定文件名
        data_type = self.data_config.get('data_type', 'random')
        if data_type == 'sequential':
            data_filename = f"{self.dataset_name.lower()}_data_sequential.npy"
            label_filename = f"{self.dataset_name.lower()}_label_sequential.npy"
        else:
            data_filename = f"{self.dataset_name.lower()}_data.npy"
            label_filename = f"{self.dataset_name.lower()}_label.npy"

        data_path = os.path.join(self.dataset_path, split, data_filename)
        label_path = os.path.join(self.dataset_path, split, label_filename)

        if not os.path.exists(data_path) or not os.path.exists(label_path):
            raise FileNotFoundError(f"数据文件不存在: {data_path} 或 {label_path}")

        data = np.load(data_path)
        labels = np.load(label_path)

        logger.info(f"加载 {split} 数据 ({data_type}): {data.shape}, 标签: {labels.shape}")

        return data, labels
    
    def _truncate_signals(self, data: np.ndarray) -> np.ndarray:
        """
        截取信号到指定长度

        Args:
            data: 原始信号数据，形状为 (N, original_length)

        Returns:
            截取后的信号数据，形状为 (N, signal_length)
        """
        current_length = data.shape[1]
        target_length = self.data_config['signal_length']

        # 如果当前长度等于目标长度，无需截取
        if current_length == target_length:
            logger.info(f"信号长度已匹配，无需截取: {current_length}")
            return data

        # 如果当前长度小于目标长度，报错
        if current_length < target_length:
            raise ValueError(f"信号长度不足：当前{current_length} < 目标{target_length}")

        # 根据数据类型选择截取方式
        if self.data_type == 'sequential':
            # 顺序数据：从开头截取
            logger.info(f"顺序数据从开头截取: {current_length} -> {target_length}")
            return data[:, :target_length]
        else:
            # 随机数据：随机截取
            truncated_data = []
            for signal in data:
                start_idx = random.randint(0, current_length - target_length)
                truncated_signal = signal[start_idx:start_idx + target_length]
                truncated_data.append(truncated_signal)

            logger.info(f"随机数据随机截取完成: {current_length} -> {target_length}")
            return np.array(truncated_data)
    
    def _limit_samples_per_class(self, data: np.ndarray, labels: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        限制每个类别的样本数量，支持健康样本和故障样本分别配置

        Args:
            data: 信号数据
            labels: 标签数据

        Returns:
            限制后的数据和标签
        """
        # 获取故障样本配置
        fault_config = self.data_config.get('fault_samples', {})
        max_fault_samples_raw = fault_config.get('max_fault_samples_per_class',
                                                self.data_config.get('max_samples_per_class', 10))  # 向后兼容

        # 处理列表配置（取第一个值）
        if isinstance(max_fault_samples_raw, list):
            max_fault_samples = max_fault_samples_raw[0] if max_fault_samples_raw else 10
        else:
            max_fault_samples = max_fault_samples_raw

        # 获取健康样本配置
        healthy_config = self.data_config.get('healthy_samples', {})
        max_healthy_samples_raw = healthy_config.get('max_healthy_samples', 0)
        healthy_label = healthy_config.get('healthy_label', 0)

        # 处理健康样本数量的特殊值
        if max_healthy_samples_raw == -1:
            # -1表示与故障样本数量保持一致
            max_healthy_samples = max_fault_samples
            logger.info(f"健康样本数量设置为-1，自动匹配故障样本数量: {max_healthy_samples}")
        elif max_healthy_samples_raw == 0:
            # 0表示不使用健康样本
            max_healthy_samples = 0
            logger.info("健康样本数量设置为0，扩散训练中不使用健康样本")
        else:
            # 大于0表示使用指定数量
            max_healthy_samples = max_healthy_samples_raw
            logger.info(f"健康样本数量设置为指定值: {max_healthy_samples}")

        # 自动根据健康样本数量确定是否使用健康样本
        use_healthy = max_healthy_samples > 0

        if max_fault_samples <= 0 and max_healthy_samples <= 0:
            return data, labels

        unique_labels = np.unique(labels)
        selected_indices = []

        logger.info(f"样本配置: 故障样本每类最多{max_fault_samples}个, 健康样本最多{max_healthy_samples}个")
        logger.info(f"健康样本使用状态: {'启用' if use_healthy else '禁用'}")

        for label in unique_labels:
            label_indices = np.where(labels.flatten() == label)[0]

            # 判断是否为健康样本
            is_healthy = (label == healthy_label)

            if is_healthy and not use_healthy:
                # 跳过健康样本
                logger.info(f"类别 {label} (健康样本): 跳过，健康样本数量设为0")
                continue

            # 确定当前类别的最大样本数
            if is_healthy:
                current_max_samples = max_healthy_samples
                sample_type = "健康样本"
            else:
                current_max_samples = max_fault_samples
                sample_type = "故障样本"

            if current_max_samples <= 0:
                logger.info(f"类别 {label} ({sample_type}): 跳过，最大样本数设为0")
                continue

            if len(label_indices) > current_max_samples:
                # 根据配置选择样本选择方式
                sample_selection = self.data_config.get('sample_selection', 'sequential')

                if sample_selection == 'random':
                    # 随机选择指定数量的样本
                    selected = np.random.choice(label_indices, current_max_samples, replace=False)
                    selected_indices.extend(selected)
                    logger.info(f"类别 {label} ({sample_type}): 从 {len(label_indices)} 个样本中随机选择 {current_max_samples} 个")
                else:  # sequential
                    # 顺序选择指定数量的样本（从前面开始选择）
                    selected = label_indices[:current_max_samples]
                    selected_indices.extend(selected)
                    logger.info(f"类别 {label} ({sample_type}): 从 {len(label_indices)} 个样本中顺序选择前 {current_max_samples} 个")
            else:
                selected_indices.extend(label_indices)
                logger.info(f"类别 {label} ({sample_type}): 使用全部 {len(label_indices)} 个样本")

        selected_indices = np.array(selected_indices)
        return data[selected_indices], labels[selected_indices]
    
    def _normalize_data(self, train_data: np.ndarray, test_data: Optional[np.ndarray] = None) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """
        归一化数据
        
        Args:
            train_data: 训练数据
            test_data: 测试数据（可选）
            
        Returns:
            归一化后的训练数据和测试数据
        """
        if not self.data_config['normalize'] or self.scaler is None:
            return train_data, test_data
        
        # 重塑数据以适应scaler
        original_shape = train_data.shape
        train_data_reshaped = train_data.reshape(-1, train_data.shape[-1])
        
        # 拟合并转换训练数据
        train_data_normalized = self.scaler.fit_transform(train_data_reshaped)
        train_data_normalized = train_data_normalized.reshape(original_shape)
        
        # 转换测试数据
        test_data_normalized = None
        if test_data is not None:
            test_shape = test_data.shape
            test_data_reshaped = test_data.reshape(-1, test_data.shape[-1])
            test_data_normalized = self.scaler.transform(test_data_reshaped)
            test_data_normalized = test_data_normalized.reshape(test_shape)
        
        logger.info(f"数据归一化完成，方法: {self.data_config['normalization_method']}")
        
        return train_data_normalized, test_data_normalized
    
    def load_data(self, batch_size: Optional[int] = None) -> Tuple[DataLoader, DataLoader, Dict]:
        """
        加载并预处理数据

        Args:
            batch_size: 批次大小，如果为None则使用配置中的分类器批次大小

        Returns:
            train_loader: 训练数据加载器
            test_loader: 测试数据加载器
            data_info: 数据信息字典
        """
        # 设置随机种子
        random.seed(self.config['system']['seed'])
        np.random.seed(self.config['system']['seed'])
        
        # 加载原始数据
        train_data, train_labels = self._load_raw_data('train')
        test_data, test_labels = self._load_raw_data('test')
        
        # 截取信号
        train_data = self._truncate_signals(train_data)
        test_data = self._truncate_signals(test_data)
        
        # 限制样本数量 - 只对训练集限制，测试集使用完整数据
        train_data, train_labels = self._limit_samples_per_class(train_data, train_labels)
        # 测试集不限制样本数量，使用完整数据进行评估
        
        # 归一化
        train_data, test_data = self._normalize_data(train_data, test_data)
        
        # 创建数据集
        train_dataset = VibrationDataset(train_data, train_labels)
        test_dataset = VibrationDataset(test_data, test_labels)
        
        # 确定批次大小
        if batch_size is None:
            batch_size = self.config['training']['classifier']['batch_size']

        # 创建数据加载器
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=self.config['system']['num_workers'],
            pin_memory=self.config['system']['pin_memory']
        )

        test_loader = DataLoader(
            test_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=self.config['system']['num_workers'],
            pin_memory=self.config['system']['pin_memory']
        )
        
        # 数据信息
        data_info = {
            'train_samples': len(train_dataset),
            'test_samples': len(test_dataset),
            'signal_length': self.data_config['signal_length'],
            'num_classes': self.config['dataset']['datasets'][self.dataset_name]['num_classes'],
            'class_names': self.config['dataset']['datasets'][self.dataset_name]['class_names'],
            'train_class_distribution': np.bincount(train_labels.flatten()),
            'test_class_distribution': np.bincount(test_labels.flatten())
        }
        
        logger.info(f"数据加载完成:")
        logger.info(f"  训练样本: {data_info['train_samples']}")
        logger.info(f"  测试样本: {data_info['test_samples']}")
        logger.info(f"  信号长度: {data_info['signal_length']}")
        logger.info(f"  类别数: {data_info['num_classes']}")
        
        return train_loader, test_loader, data_info
    
    def get_scaler(self):
        """获取归一化器"""
        return self.scaler


class CombinedDataLoader:
    """组合数据集加载器（真实样本 + 增强样本）"""

    def __init__(self, config: Dict):
        """
        初始化组合数据加载器

        Args:
            config: 配置字典
        """
        self.config = config
        self.dataset_name = config['dataset']['name']
        self.method_name = config['augmentation']['method']

    def load_combined_dataset(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        加载组合数据集

        Returns:
            combined_data: 组合数据
            combined_labels: 组合标签
        """
        # 构建组合数据集路径（与generated_samples并列）
        combined_dir = os.path.join(
            'combined_dataset',
            self.dataset_name,
            self.method_name
        )

        data_path = os.path.join(combined_dir, 'combined_data.npy')
        label_path = os.path.join(combined_dir, 'combined_labels.npy')

        if not os.path.exists(data_path) or not os.path.exists(label_path):
            raise FileNotFoundError(f"组合数据集不存在: {combined_dir}")

        combined_data = np.load(data_path)
        combined_labels = np.load(label_path)

        logger.info(f"组合数据集加载完成:")
        logger.info(f"  数据形状: {combined_data.shape}")
        logger.info(f"  标签形状: {combined_labels.shape}")
        logger.info(f"  类别分布: {dict(zip(*np.unique(combined_labels, return_counts=True)))}")

        return combined_data, combined_labels

    def create_data_loaders(self, combined_data: np.ndarray, combined_labels: np.ndarray,
                           test_data: np.ndarray, test_labels: np.ndarray,
                           train_val_split: float = 0.7) -> Tuple[DataLoader, DataLoader, DataLoader]:
        """
        创建训练、验证和测试数据加载器

        Args:
            combined_data: 组合训练数据
            combined_labels: 组合训练标签
            test_data: 测试数据
            test_labels: 测试标签
            train_val_split: 训练验证分割比例

        Returns:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            test_loader: 测试数据加载器
        """
        # 创建组合数据集
        combined_dataset = VibrationDataset(combined_data, combined_labels)

        # 分割为训练和验证
        train_size = int(train_val_split * len(combined_dataset))
        val_size = len(combined_dataset) - train_size

        train_subset, val_subset = torch.utils.data.random_split(
            combined_dataset, [train_size, val_size],
            generator=torch.Generator().manual_seed(self.config['system']['seed'])
        )

        # 创建测试数据集
        test_dataset = VibrationDataset(test_data, test_labels)

        # 创建数据加载器
        batch_size = self.config['training']['classifier']['batch_size']
        num_workers = self.config['system']['num_workers']
        pin_memory = self.config['system']['pin_memory']

        train_loader = DataLoader(
            train_subset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=pin_memory
        )

        val_loader = DataLoader(
            val_subset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=pin_memory
        )

        test_loader = DataLoader(
            test_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=pin_memory
        )

        logger.info(f"组合数据加载器创建完成:")
        logger.info(f"  训练样本: {len(train_subset)}")
        logger.info(f"  验证样本: {len(val_subset)}")
        logger.info(f"  测试样本: {len(test_dataset)}")

        return train_loader, val_loader, test_loader
