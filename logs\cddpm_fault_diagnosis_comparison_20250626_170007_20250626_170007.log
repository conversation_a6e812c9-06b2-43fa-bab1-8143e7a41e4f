2025-06-26 17:00:07,489 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\cddpm_fault_diagnosis_comparison_20250626_170007_20250626_170007.log
2025-06-26 17:00:07,489 - common.experiment_manager - INFO - 检测到只有生成数量参数变化，将重用扩散模型
2025-06-26 17:00:07,489 - __main__ - INFO - ================================================================================
2025-06-26 17:00:07,489 - __main__ - INFO - 检测到对比实验配置
2025-06-26 17:00:07,490 - __main__ - INFO - 实验名称: cddpm_fault_diagnosis
2025-06-26 17:00:07,490 - __main__ - INFO - 数据集: KAT
2025-06-26 17:00:07,490 - __main__ - INFO - 总实验数: 2
2025-06-26 17:00:07,490 - __main__ - INFO - 对比参数: ['augmentation.num_generated_per_class']
2025-06-26 17:00:07,490 - __main__ - INFO - 🔥 扩散模型重用: 是
2025-06-26 17:00:07,490 - __main__ - INFO - ================================================================================
2025-06-26 17:00:07,490 - __main__ - INFO - 缓存配置文件保存在: cache\20250626_170007
2025-06-26 17:00:07,491 - common.results_manager - INFO - 原始配置文件已复制到结果目录: results\KAT\20250626_170007\configs\original_config.yaml
2025-06-26 17:00:07,491 - __main__ - INFO - 🔥 启用扩散模型重用模式
2025-06-26 17:00:07,492 - common.experiment_manager - INFO - 实验分组完成，共 1 个训练数据组
2025-06-26 17:00:07,492 - common.experiment_manager - INFO -   组 1: 2 个实验 - dataset=KAT_fault=3_healthy=-1_length=1024
2025-06-26 17:00:07,492 - __main__ - INFO - ======================================================================
2025-06-26 17:00:07,492 - __main__ - INFO - 处理实验组: dataset=KAT_fault=3_healthy=-1_length=1024
2025-06-26 17:00:07,492 - __main__ - INFO - 该组包含 2 个实验
2025-06-26 17:00:07,492 - __main__ - INFO - ======================================================================
2025-06-26 17:00:07,492 - __main__ - INFO - 🚀 训练扩散模型（该组唯一一次）...
2025-06-26 17:00:07,493 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 17:00:07,493 - common.data_loader - INFO - 数据类型: sequential, 样本选择方式: sequential
2025-06-26 17:00:07,500 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 17:00:07,507 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 17:00:07,508 - common.data_loader - INFO - 信号长度已匹配，无需截取: 1024
2025-06-26 17:00:07,508 - common.data_loader - INFO - 信号长度已匹配，无需截取: 1024
2025-06-26 17:00:07,508 - common.data_loader - INFO - 健康样本数量设置为-1，自动匹配故障样本数量: 3
2025-06-26 17:00:07,508 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 17:00:07,508 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 17:00:07,508 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:00:07,509 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:00:07,509 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:00:07,509 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:00:07,509 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:00:07,509 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:00:07,509 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 17:00:07,509 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:00:07,515 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 17:00:07,523 - common.data_loader - INFO - 数据加载完成:
2025-06-26 17:00:07,523 - common.data_loader - INFO -   训练样本: 24
2025-06-26 17:00:07,523 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 17:00:07,523 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 17:00:07,523 - common.data_loader - INFO -   类别数: 8
2025-06-26 17:00:07,526 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 17:00:07,527 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 17:00:07,527 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 17:00:07,527 - models.cddpm - INFO -   类别数量: 8
2025-06-26 17:00:07,732 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 17:00:07,732 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 17:00:07,733 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 17:00:07,734 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 17:00:07,993 - __main__ - INFO - 启用channels_last内存格式
2025-06-26 17:00:07,994 - __main__ - INFO - 启用自动混合精度训练
2025-06-26 17:00:07,994 - __main__ - INFO - 扩散模型参数数量: 44,324,929
2025-06-26 17:00:07,994 - __main__ - INFO - 开始训练扩散模型...
2025-06-26 17:00:07,994 - __main__ - INFO - 最佳模型判断标准: weighted_loss (min)
2025-06-26 17:00:07,995 - __main__ - INFO - 加权损失配置: 训练损失权重=0.7, 验证损失权重=0.3
2025-06-26 17:00:16,881 - __main__ - INFO - Epoch   1/20000 | Train Loss: 0.821613 | Val Loss: 0.744884 | Weighted Loss: 0.798594 | LR: 1.00e-04 | Time: 00:08 | Save: Best✓(weighted_loss=0.798594)
2025-06-26 17:00:17,898 - __main__ - INFO - Epoch   2/20000 | Train Loss: 0.776075 | Val Loss: 0.745829 | Weighted Loss: 0.767002 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.767002)
2025-06-26 17:00:18,914 - __main__ - INFO - Epoch   3/20000 | Train Loss: 0.753688 | Val Loss: 0.727417 | Weighted Loss: 0.745807 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.745807)
2025-06-26 17:00:20,043 - __main__ - INFO - Epoch   4/20000 | Train Loss: 0.749071 | Val Loss: 0.673177 | Weighted Loss: 0.726302 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.726302)
2025-06-26 17:00:20,921 - __main__ - INFO - Epoch   5/20000 | Train Loss: 0.722245 | Val Loss: 0.662566 | Weighted Loss: 0.704341 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.704341)
2025-06-26 17:00:22,022 - __main__ - INFO - Epoch   6/20000 | Train Loss: 0.703136 | Val Loss: 0.683199 | Weighted Loss: 0.697155 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.697155)
2025-06-26 17:00:22,883 - __main__ - INFO - Epoch   7/20000 | Train Loss: 0.668809 | Val Loss: 0.605941 | Weighted Loss: 0.649949 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.649949)
2025-06-26 17:00:23,789 - __main__ - INFO - Epoch   8/20000 | Train Loss: 0.682044 | Val Loss: 0.557653 | Weighted Loss: 0.644727 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.644727)
2025-06-26 17:00:24,975 - __main__ - INFO - Epoch   9/20000 | Train Loss: 0.648346 | Val Loss: 0.574968 | Weighted Loss: 0.626333 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.626333)
2025-06-26 17:00:25,857 - __main__ - INFO - Epoch  10/20000 | Train Loss: 0.590509 | Val Loss: 0.541598 | Weighted Loss: 0.575836 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.575836)
2025-06-26 17:00:26,668 - __main__ - INFO - Epoch  11/20000 | Train Loss: 0.590852 | Val Loss: 0.489901 | Weighted Loss: 0.560567 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.560567)
2025-06-26 17:00:26,951 - __main__ - INFO - Epoch  12/20000 | Train Loss: 0.599600 | Val Loss: 0.487087 | Weighted Loss: 0.565846 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:00:27,842 - __main__ - INFO - Epoch  13/20000 | Train Loss: 0.536536 | Val Loss: 0.494332 | Weighted Loss: 0.523875 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.523875)
2025-06-26 17:00:28,121 - __main__ - INFO - Epoch  14/20000 | Train Loss: 0.562335 | Val Loss: 0.508708 | Weighted Loss: 0.546247 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:00:28,987 - __main__ - INFO - Epoch  15/20000 | Train Loss: 0.518021 | Val Loss: 0.460514 | Weighted Loss: 0.500769 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.500769)
2025-06-26 17:00:29,270 - __main__ - INFO - Epoch  16/20000 | Train Loss: 0.532066 | Val Loss: 0.461141 | Weighted Loss: 0.510788 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:00:30,152 - __main__ - INFO - Epoch  17/20000 | Train Loss: 0.473458 | Val Loss: 0.378048 | Weighted Loss: 0.444835 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.444835)
2025-06-26 17:00:32,208 - __main__ - INFO - Epoch  18/20000 | Train Loss: 0.384269 | Val Loss: 0.349678 | Weighted Loss: 0.373892 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.373892)
