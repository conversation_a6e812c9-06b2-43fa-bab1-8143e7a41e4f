"""
配置管理器
提供配置验证和基本管理功能
"""

import logging
from typing import Dict, Any
import copy

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器，提供配置验证和基本管理功能"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化配置管理器

        Args:
            config: 原始配置字典
        """
        self.original_config = copy.deepcopy(config)
        self.config = config

    
    def validate_config(self) -> bool:
        """
        验证配置的合理性
        
        Returns:
            配置是否有效
        """
        try:
            # 验证基本结构
            required_sections = ['dataset', 'augmentation', 'models', 'training']
            for section in required_sections:
                if section not in self.config:
                    logger.error(f"缺少必需的配置节: {section}")
                    return False
            
            # 验证扩散模型配置
            diffusion_config = self.config['models']['diffusion']
            if diffusion_config.get('hidden_dim', 0) <= 0:
                logger.error("hidden_dim必须大于0")
                return False
            
            # 验证训练配置
            training_config = self.config['training']['diffusion']
            if training_config.get('epochs', 0) <= 0:
                logger.error("epochs必须大于0")
                return False
            
            if training_config.get('learning_rate', 0) <= 0:
                logger.error("learning_rate必须大于0")
                return False
            
            # 验证CDDPM配置
            cddpm_config = self.config['augmentation']['cddpm']
            if cddpm_config.get('timesteps', 0) <= 0:
                logger.error("timesteps必须大于0")
                return False
            
            logger.info("配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False
    
    def get_model_summary(self) -> Dict[str, Any]:
        """
        获取模型配置摘要
        
        Returns:
            模型配置摘要
        """
        diffusion_config = self.config['models']['diffusion']
        training_config = self.config['training']['diffusion']
        cddpm_config = self.config['augmentation']['cddpm']
        
        summary = {
            'model_type': 'CDDPM',
            'model_params': {
                'hidden_dim': diffusion_config.get('hidden_dim', 64),
                'encoder_channels': diffusion_config.get('encoder_channels', [64, 128, 256, 512, 1024]),
                'num_layers_per_block': diffusion_config.get('num_layers_per_block', 2),
                'dropout': diffusion_config.get('dropout', 0.1),
                'use_skip_connections': diffusion_config.get('use_skip_connections', True)
            },
            'training_params': {
                'epochs': training_config.get('epochs', 10000),
                'batch_size': training_config.get('batch_size', 64),
                'learning_rate': training_config.get('learning_rate', 0.00001),
                'weight_decay': training_config.get('weight_decay', 0.0001)
            },
            'diffusion_params': {
                'timesteps': cddpm_config.get('timesteps', 1000),
                'beta_schedule': cddpm_config.get('beta_schedule', 'linear'),
                'unconditional_prob': cddpm_config.get('unconditional_prob', 0.1),
                'guidance_scale': cddpm_config.get('guidance_scale', 1.0)
            }
        }
        
        return summary
    



def create_config_manager(config: Dict[str, Any]) -> ConfigManager:
    """
    创建配置管理器的工厂函数

    Args:
        config: 配置字典

    Returns:
        配置管理器实例
    """
    manager = ConfigManager(config)

    # 验证配置
    if not manager.validate_config():
        logger.error("配置验证失败，请检查配置文件")
        raise ValueError("配置验证失败")

    # 打印配置摘要
    summary = manager.get_model_summary()
    logger.info(f"模型配置摘要: {summary}")

    return manager
