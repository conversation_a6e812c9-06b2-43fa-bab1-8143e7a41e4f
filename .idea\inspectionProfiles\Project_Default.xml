<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="14">
            <item index="0" class="java.lang.String" itemvalue="PyYAML" />
            <item index="1" class="java.lang.String" itemvalue="torchvision" />
            <item index="2" class="java.lang.String" itemvalue="torchmetrics" />
            <item index="3" class="java.lang.String" itemvalue="transformers" />
            <item index="4" class="java.lang.String" itemvalue="thop" />
            <item index="5" class="java.lang.String" itemvalue="pytorch-fid" />
            <item index="6" class="java.lang.String" itemvalue="black" />
            <item index="7" class="java.lang.String" itemvalue="flake8" />
            <item index="8" class="java.lang.String" itemvalue="omegaconf" />
            <item index="9" class="java.lang.String" itemvalue="tensorboard" />
            <item index="10" class="java.lang.String" itemvalue="ptflops" />
            <item index="11" class="java.lang.String" itemvalue="umap-learn" />
            <item index="12" class="java.lang.String" itemvalue="wandb" />
            <item index="13" class="java.lang.String" itemvalue="einops" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>