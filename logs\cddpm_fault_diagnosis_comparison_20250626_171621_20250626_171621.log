2025-06-26 17:16:21,254 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\cddpm_fault_diagnosis_comparison_20250626_171621_20250626_171621.log
2025-06-26 17:16:21,254 - common.experiment_manager - INFO - 检测到只有生成数量参数变化，将重用扩散模型
2025-06-26 17:16:21,254 - __main__ - INFO - ================================================================================
2025-06-26 17:16:21,254 - __main__ - INFO - 检测到对比实验配置
2025-06-26 17:16:21,255 - __main__ - INFO - 实验名称: cddpm_fault_diagnosis
2025-06-26 17:16:21,255 - __main__ - INFO - 数据集: KAT
2025-06-26 17:16:21,255 - __main__ - INFO - 总实验数: 2
2025-06-26 17:16:21,255 - __main__ - INFO - 对比参数: ['augmentation.num_generated_per_class']
2025-06-26 17:16:21,255 - __main__ - INFO - 🔥 扩散模型重用: 是
2025-06-26 17:16:21,255 - __main__ - INFO - ================================================================================
2025-06-26 17:16:21,255 - __main__ - INFO - 缓存配置文件保存在: cache\20250626_171621
2025-06-26 17:16:21,256 - common.results_manager - INFO - 原始配置文件已复制到结果目录: results\KAT\20250626_171621\configs\original_config.yaml
2025-06-26 17:16:21,256 - __main__ - INFO - 🔥 启用扩散模型重用模式
2025-06-26 17:16:21,256 - common.experiment_manager - INFO - 实验分组完成，共 1 个训练数据组
2025-06-26 17:16:21,256 - common.experiment_manager - INFO -   组 1: 2 个实验 - dataset=KAT_fault=3_healthy=-1_length=1024
2025-06-26 17:16:21,256 - __main__ - INFO - ======================================================================
2025-06-26 17:16:21,256 - __main__ - INFO - 处理实验组: dataset=KAT_fault=3_healthy=-1_length=1024
2025-06-26 17:16:21,256 - __main__ - INFO - 该组包含 2 个实验
2025-06-26 17:16:21,256 - __main__ - INFO - ======================================================================
2025-06-26 17:16:21,256 - __main__ - INFO - 🚀 训练扩散模型（该组唯一一次）...
2025-06-26 17:16:21,257 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 17:16:21,257 - common.data_loader - INFO - 数据类型: sequential, 样本选择方式: sequential
2025-06-26 17:16:21,259 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 17:16:21,261 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 17:16:21,261 - common.data_loader - INFO - 信号长度已匹配，无需截取: 1024
2025-06-26 17:16:21,262 - common.data_loader - INFO - 信号长度已匹配，无需截取: 1024
2025-06-26 17:16:21,262 - common.data_loader - INFO - 健康样本数量设置为-1，自动匹配故障样本数量: 3
2025-06-26 17:16:21,262 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 17:16:21,262 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 17:16:21,262 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:16:21,262 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:16:21,262 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:16:21,262 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:16:21,262 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:16:21,262 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:16:21,262 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 17:16:21,262 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:16:21,267 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 17:16:21,271 - common.data_loader - INFO - 数据加载完成:
2025-06-26 17:16:21,272 - common.data_loader - INFO -   训练样本: 24
2025-06-26 17:16:21,272 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 17:16:21,272 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 17:16:21,272 - common.data_loader - INFO -   类别数: 8
2025-06-26 17:16:21,273 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 17:16:21,273 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 17:16:21,273 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 17:16:21,274 - models.cddpm - INFO -   类别数量: 8
2025-06-26 17:16:21,448 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 17:16:21,448 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 17:16:21,448 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 17:16:21,449 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 17:16:21,592 - __main__ - INFO - 启用channels_last内存格式
2025-06-26 17:16:21,592 - __main__ - INFO - 启用自动混合精度训练
2025-06-26 17:16:21,593 - __main__ - INFO - 扩散模型参数数量: 44,324,929
2025-06-26 17:16:21,593 - __main__ - INFO - 开始训练扩散模型...
2025-06-26 17:16:21,593 - __main__ - INFO - 最佳模型判断标准: weighted_loss (min)
2025-06-26 17:16:21,593 - __main__ - INFO - 加权损失配置: 训练损失权重=0.7, 验证损失权重=0.3
2025-06-26 17:16:26,498 - __main__ - INFO - Epoch   1/20 | Train Loss: 0.821613 | Val Loss: 0.744884 | Weighted Loss: 0.798594 | LR: 1.00e-04 | Time: 00:04 | Save: Best✓(weighted_loss=0.798594)
2025-06-26 17:16:27,285 - __main__ - INFO - Epoch   2/20 | Train Loss: 0.776075 | Val Loss: 0.745829 | Weighted Loss: 0.767002 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.767002)
2025-06-26 17:16:28,060 - __main__ - INFO - Epoch   3/20 | Train Loss: 0.753688 | Val Loss: 0.727417 | Weighted Loss: 0.745807 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.745807)
2025-06-26 17:16:28,805 - __main__ - INFO - Epoch   4/20 | Train Loss: 0.749071 | Val Loss: 0.673177 | Weighted Loss: 0.726302 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.726302)
2025-06-26 17:16:29,730 - __main__ - INFO - Epoch   5/20 | Train Loss: 0.722245 | Val Loss: 0.662566 | Weighted Loss: 0.704341 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.704341)
2025-06-26 17:16:30,462 - __main__ - INFO - Epoch   6/20 | Train Loss: 0.703136 | Val Loss: 0.683199 | Weighted Loss: 0.697155 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.697155)
2025-06-26 17:16:31,251 - __main__ - INFO - Epoch   7/20 | Train Loss: 0.668809 | Val Loss: 0.605941 | Weighted Loss: 0.649949 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.649949)
2025-06-26 17:16:31,999 - __main__ - INFO - Epoch   8/20 | Train Loss: 0.682044 | Val Loss: 0.557653 | Weighted Loss: 0.644727 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.644727)
2025-06-26 17:16:32,736 - __main__ - INFO - Epoch   9/20 | Train Loss: 0.648346 | Val Loss: 0.574968 | Weighted Loss: 0.626333 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.626333)
2025-06-26 17:16:33,469 - __main__ - INFO - Epoch  10/20 | Train Loss: 0.590509 | Val Loss: 0.541598 | Weighted Loss: 0.575836 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.575836)
2025-06-26 17:16:34,242 - __main__ - INFO - Epoch  11/20 | Train Loss: 0.590852 | Val Loss: 0.489901 | Weighted Loss: 0.560567 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.560567)
2025-06-26 17:16:34,531 - __main__ - INFO - Epoch  12/20 | Train Loss: 0.599600 | Val Loss: 0.487087 | Weighted Loss: 0.565846 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:16:35,285 - __main__ - INFO - Epoch  13/20 | Train Loss: 0.536536 | Val Loss: 0.494332 | Weighted Loss: 0.523875 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.523875)
2025-06-26 17:16:35,571 - __main__ - INFO - Epoch  14/20 | Train Loss: 0.562335 | Val Loss: 0.508708 | Weighted Loss: 0.546247 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:16:36,373 - __main__ - INFO - Epoch  15/20 | Train Loss: 0.518021 | Val Loss: 0.460514 | Weighted Loss: 0.500769 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.500769)
2025-06-26 17:16:36,666 - __main__ - INFO - Epoch  16/20 | Train Loss: 0.532066 | Val Loss: 0.461141 | Weighted Loss: 0.510788 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:16:37,418 - __main__ - INFO - Epoch  17/20 | Train Loss: 0.473458 | Val Loss: 0.378048 | Weighted Loss: 0.444835 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.444835)
2025-06-26 17:16:38,268 - __main__ - INFO - Epoch  18/20 | Train Loss: 0.384269 | Val Loss: 0.349678 | Weighted Loss: 0.373892 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.373892)
2025-06-26 17:16:38,547 - __main__ - INFO - Epoch  19/20 | Train Loss: 0.469238 | Val Loss: 0.299757 | Weighted Loss: 0.418394 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:16:39,294 - __main__ - INFO - Epoch  20/20 | Train Loss: 0.361507 | Val Loss: 0.275725 | Weighted Loss: 0.335772 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.335772)
2025-06-26 17:16:39,295 - __main__ - INFO - 扩散模型训练完成，用时: 00:17
2025-06-26 17:16:39,295 - __main__ - INFO - 最佳验证损失: 0.275725
2025-06-26 17:16:39,295 - __main__ - INFO - ✅ 扩散模型训练完成，保存至: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 17:16:39,296 - __main__ - INFO - ============================================================
2025-06-26 17:16:39,296 - __main__ - INFO - 开始实验 1/2 (组内 1/2)
2025-06-26 17:16:39,296 - __main__ - INFO - 实验参数: {'augmentation.num_generated_per_class': 5}
2025-06-26 17:16:39,296 - __main__ - INFO - 🔄 重用扩散模型: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 17:16:39,296 - __main__ - INFO - ============================================================
2025-06-26 17:16:39,307 - __main__ - INFO - 缓存配置已保存: cache\20250626_171621\experiment_001_config.yaml
2025-06-26 17:16:39,307 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 17:16:39,307 - common.data_loader - INFO - 数据类型: sequential, 样本选择方式: sequential
2025-06-26 17:16:39,313 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 17:16:39,319 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 17:16:39,319 - common.data_loader - INFO - 信号长度已匹配，无需截取: 1024
2025-06-26 17:16:39,319 - common.data_loader - INFO - 信号长度已匹配，无需截取: 1024
2025-06-26 17:16:39,319 - common.data_loader - INFO - 健康样本数量设置为-1，自动匹配故障样本数量: 3
2025-06-26 17:16:39,320 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 17:16:39,320 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 17:16:39,320 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:16:39,320 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:16:39,320 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:16:39,320 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:16:39,320 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:16:39,321 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:16:39,321 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 17:16:39,321 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:16:39,325 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 17:16:39,326 - common.data_loader - INFO - 数据加载完成:
2025-06-26 17:16:39,326 - common.data_loader - INFO -   训练样本: 24
2025-06-26 17:16:39,326 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 17:16:39,326 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 17:16:39,326 - common.data_loader - INFO -   类别数: 8
2025-06-26 17:16:39,327 - __main__ - INFO - 训练基线分类器...
2025-06-26 17:16:39,353 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 17:16:39,353 - __main__ - INFO - 开始训练分类器...
2025-06-26 17:16:39,627 - __main__ - INFO - Epoch   1/10 | Train Loss: 2.0765 Acc: 0.1250 | Val Loss: 2.0801 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓
2025-06-26 17:16:39,677 - __main__ - INFO - Epoch   2/10 | Train Loss: 2.0783 Acc: 0.2500 | Val Loss: 2.0811 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:16:39,723 - __main__ - INFO - Epoch   3/10 | Train Loss: 2.0645 Acc: 0.1875 | Val Loss: 2.0824 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:16:39,771 - __main__ - INFO - Epoch   4/10 | Train Loss: 2.0663 Acc: 0.3125 | Val Loss: 2.0841 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:16:39,816 - __main__ - INFO - Epoch   5/10 | Train Loss: 2.0646 Acc: 0.0625 | Val Loss: 2.0861 Acc: 0.1250 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:16:39,866 - __main__ - INFO - Epoch   6/10 | Train Loss: 2.0490 Acc: 0.2500 | Val Loss: 2.0884 Acc: 0.1250 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:16:39,916 - __main__ - INFO - Epoch   7/10 | Train Loss: 2.0466 Acc: 0.2500 | Val Loss: 2.0910 Acc: 0.0000 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:16:39,963 - __main__ - INFO - Epoch   8/10 | Train Loss: 2.0383 Acc: 0.3125 | Val Loss: 2.0938 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:16:40,017 - __main__ - INFO - Epoch   9/10 | Train Loss: 2.0364 Acc: 0.2500 | Val Loss: 2.0970 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:16:40,068 - __main__ - INFO - Epoch  10/10 | Train Loss: 2.0278 Acc: 0.3750 | Val Loss: 2.1006 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:16:40,068 - __main__ - INFO - 分类器训练完成，用时: 00:00
2025-06-26 17:16:40,069 - __main__ - INFO - 最佳验证准确率: 0.1250
2025-06-26 17:16:40,069 - __main__ - INFO - 🔄 使用预训练扩散模型生成样本: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 17:16:40,069 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 17:16:40,069 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 17:16:40,069 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 17:16:40,069 - models.cddpm - INFO -   类别数量: 8
2025-06-26 17:16:40,213 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 17:16:40,213 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 17:16:40,213 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 17:16:40,213 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 17:16:40,496 - __main__ - INFO - 扩散模型已加载: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 17:16:40,497 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 5 个...
2025-06-26 17:16:40,497 - __main__ - INFO - 生成故障样本+健康样本，健康样本标签=0，每类生成5个
2025-06-26 17:16:40,497 - __main__ - INFO - 生成类别 0 的样本...
2025-06-26 17:16:58,563 - __main__ - INFO - 生成类别 1 的样本...
2025-06-26 17:17:16,338 - __main__ - INFO - 生成类别 2 的样本...
