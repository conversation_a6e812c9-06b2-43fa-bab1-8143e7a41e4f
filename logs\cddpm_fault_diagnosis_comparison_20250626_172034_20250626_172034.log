2025-06-26 17:20:34,516 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\cddpm_fault_diagnosis_comparison_20250626_172034_20250626_172034.log
2025-06-26 17:20:34,516 - common.experiment_manager - INFO - 检测到只有生成数量参数变化，将重用扩散模型
2025-06-26 17:20:34,517 - __main__ - INFO - ================================================================================
2025-06-26 17:20:34,517 - __main__ - INFO - 检测到对比实验配置
2025-06-26 17:20:34,517 - __main__ - INFO - 实验名称: cddpm_fault_diagnosis
2025-06-26 17:20:34,517 - __main__ - INFO - 数据集: KAT
2025-06-26 17:20:34,517 - __main__ - INFO - 总实验数: 2
2025-06-26 17:20:34,517 - __main__ - INFO - 对比参数: ['augmentation.num_generated_per_class']
2025-06-26 17:20:34,517 - __main__ - INFO - 🔥 扩散模型重用: 是
2025-06-26 17:20:34,517 - __main__ - INFO - ================================================================================
2025-06-26 17:20:34,518 - __main__ - INFO - 缓存配置文件保存在: cache\20250626_172034
2025-06-26 17:20:34,518 - common.results_manager - INFO - 原始配置文件已复制到结果目录: results\KAT\20250626_172034\configs\original_config.yaml
2025-06-26 17:20:34,518 - __main__ - INFO - 🔥 启用扩散模型重用模式
2025-06-26 17:20:34,519 - common.experiment_manager - INFO - 实验分组完成，共 1 个训练数据组
2025-06-26 17:20:34,519 - common.experiment_manager - INFO -   组 1: 2 个实验 - dataset=KAT_fault=3_healthy=-1_length=1024
2025-06-26 17:20:34,519 - __main__ - INFO - ======================================================================
2025-06-26 17:20:34,519 - __main__ - INFO - 处理实验组: dataset=KAT_fault=3_healthy=-1_length=1024
2025-06-26 17:20:34,519 - __main__ - INFO - 该组包含 2 个实验
2025-06-26 17:20:34,519 - __main__ - INFO - ======================================================================
2025-06-26 17:20:34,519 - __main__ - INFO - 🚀 训练扩散模型（该组唯一一次）...
2025-06-26 17:20:34,519 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 17:20:34,519 - common.data_loader - INFO - 数据类型: sequential, 样本选择方式: sequential
2025-06-26 17:20:34,524 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 17:20:34,530 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 17:20:34,530 - common.data_loader - INFO - 信号长度已匹配，无需截取: 1024
2025-06-26 17:20:34,530 - common.data_loader - INFO - 信号长度已匹配，无需截取: 1024
2025-06-26 17:20:34,530 - common.data_loader - INFO - 健康样本数量设置为-1，自动匹配故障样本数量: 3
2025-06-26 17:20:34,530 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 17:20:34,530 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 17:20:34,530 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:34,530 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:34,530 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:34,530 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:34,530 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:34,530 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:34,531 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 17:20:34,531 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:34,535 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 17:20:34,539 - common.data_loader - INFO - 数据加载完成:
2025-06-26 17:20:34,539 - common.data_loader - INFO -   训练样本: 24
2025-06-26 17:20:34,539 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 17:20:34,539 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 17:20:34,539 - common.data_loader - INFO -   类别数: 8
2025-06-26 17:20:34,540 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 17:20:34,540 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 17:20:34,540 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 17:20:34,540 - models.cddpm - INFO -   类别数量: 8
2025-06-26 17:20:34,712 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 17:20:34,712 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 17:20:34,712 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 17:20:34,712 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 17:20:34,860 - __main__ - INFO - 启用channels_last内存格式
2025-06-26 17:20:34,861 - __main__ - INFO - 启用自动混合精度训练
2025-06-26 17:20:34,862 - __main__ - INFO - 扩散模型参数数量: 44,324,929
2025-06-26 17:20:34,862 - __main__ - INFO - 开始训练扩散模型...
2025-06-26 17:20:34,862 - __main__ - INFO - 最佳模型判断标准: weighted_loss (min)
2025-06-26 17:20:34,862 - __main__ - INFO - 加权损失配置: 训练损失权重=0.7, 验证损失权重=0.3
2025-06-26 17:20:39,265 - __main__ - INFO - Epoch   1/20 | Train Loss: 0.821613 | Val Loss: 0.744884 | Weighted Loss: 0.798594 | LR: 1.00e-04 | Time: 00:03 | Save: Best✓(weighted_loss=0.798594)
2025-06-26 17:20:40,362 - __main__ - INFO - Epoch   2/20 | Train Loss: 0.776075 | Val Loss: 0.745829 | Weighted Loss: 0.767002 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.767002)
2025-06-26 17:20:41,316 - __main__ - INFO - Epoch   3/20 | Train Loss: 0.753688 | Val Loss: 0.727417 | Weighted Loss: 0.745807 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.745807)
2025-06-26 17:20:43,634 - __main__ - INFO - Epoch   4/20 | Train Loss: 0.749071 | Val Loss: 0.673177 | Weighted Loss: 0.726302 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.726302)
2025-06-26 17:20:44,580 - __main__ - INFO - Epoch   5/20 | Train Loss: 0.722245 | Val Loss: 0.662566 | Weighted Loss: 0.704341 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.704341)
2025-06-26 17:20:45,376 - __main__ - INFO - Epoch   6/20 | Train Loss: 0.703136 | Val Loss: 0.683199 | Weighted Loss: 0.697155 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.697155)
2025-06-26 17:20:46,150 - __main__ - INFO - Epoch   7/20 | Train Loss: 0.668809 | Val Loss: 0.605941 | Weighted Loss: 0.649949 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.649949)
2025-06-26 17:20:46,920 - __main__ - INFO - Epoch   8/20 | Train Loss: 0.682044 | Val Loss: 0.557653 | Weighted Loss: 0.644727 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.644727)
2025-06-26 17:20:47,703 - __main__ - INFO - Epoch   9/20 | Train Loss: 0.648346 | Val Loss: 0.574968 | Weighted Loss: 0.626333 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.626333)
2025-06-26 17:20:48,463 - __main__ - INFO - Epoch  10/20 | Train Loss: 0.590509 | Val Loss: 0.541598 | Weighted Loss: 0.575836 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.575836)
2025-06-26 17:20:49,251 - __main__ - INFO - Epoch  11/20 | Train Loss: 0.590852 | Val Loss: 0.489901 | Weighted Loss: 0.560567 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.560567)
2025-06-26 17:20:49,526 - __main__ - INFO - Epoch  12/20 | Train Loss: 0.599600 | Val Loss: 0.487087 | Weighted Loss: 0.565846 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:20:50,300 - __main__ - INFO - Epoch  13/20 | Train Loss: 0.536536 | Val Loss: 0.494332 | Weighted Loss: 0.523875 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.523875)
2025-06-26 17:20:50,567 - __main__ - INFO - Epoch  14/20 | Train Loss: 0.562335 | Val Loss: 0.508708 | Weighted Loss: 0.546247 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:20:51,307 - __main__ - INFO - Epoch  15/20 | Train Loss: 0.518021 | Val Loss: 0.460514 | Weighted Loss: 0.500769 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.500769)
2025-06-26 17:20:51,580 - __main__ - INFO - Epoch  16/20 | Train Loss: 0.532066 | Val Loss: 0.461141 | Weighted Loss: 0.510788 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:20:52,337 - __main__ - INFO - Epoch  17/20 | Train Loss: 0.473458 | Val Loss: 0.378048 | Weighted Loss: 0.444835 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.444835)
2025-06-26 17:20:53,219 - __main__ - INFO - Epoch  18/20 | Train Loss: 0.384269 | Val Loss: 0.349678 | Weighted Loss: 0.373892 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.373892)
2025-06-26 17:20:53,499 - __main__ - INFO - Epoch  19/20 | Train Loss: 0.469238 | Val Loss: 0.299757 | Weighted Loss: 0.418394 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:20:54,290 - __main__ - INFO - Epoch  20/20 | Train Loss: 0.361507 | Val Loss: 0.275725 | Weighted Loss: 0.335772 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.335772)
2025-06-26 17:20:54,290 - __main__ - INFO - 扩散模型训练完成，用时: 00:19
2025-06-26 17:20:54,291 - __main__ - INFO - 最佳验证损失: 0.275725
2025-06-26 17:20:54,291 - __main__ - INFO - ✅ 扩散模型训练完成，保存至: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 17:20:54,292 - __main__ - INFO - ============================================================
2025-06-26 17:20:54,292 - __main__ - INFO - 开始实验 1/2 (组内 1/2)
2025-06-26 17:20:54,292 - __main__ - INFO - 实验参数: {'augmentation.num_generated_per_class': 1}
2025-06-26 17:20:54,292 - __main__ - INFO - 🔄 重用扩散模型: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 17:20:54,292 - __main__ - INFO - ============================================================
2025-06-26 17:20:54,303 - __main__ - INFO - 缓存配置已保存: cache\20250626_172034\experiment_001_config.yaml
2025-06-26 17:20:54,303 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 17:20:54,303 - common.data_loader - INFO - 数据类型: sequential, 样本选择方式: sequential
2025-06-26 17:20:54,308 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 17:20:54,313 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 17:20:54,314 - common.data_loader - INFO - 信号长度已匹配，无需截取: 1024
2025-06-26 17:20:54,314 - common.data_loader - INFO - 信号长度已匹配，无需截取: 1024
2025-06-26 17:20:54,314 - common.data_loader - INFO - 健康样本数量设置为-1，自动匹配故障样本数量: 3
2025-06-26 17:20:54,314 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 17:20:54,314 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 17:20:54,314 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:54,315 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:54,315 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:54,315 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:54,315 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:54,315 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:54,315 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 17:20:54,315 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:54,319 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 17:20:54,320 - common.data_loader - INFO - 数据加载完成:
2025-06-26 17:20:54,320 - common.data_loader - INFO -   训练样本: 24
2025-06-26 17:20:54,321 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 17:20:54,321 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 17:20:54,321 - common.data_loader - INFO -   类别数: 8
2025-06-26 17:20:54,321 - __main__ - INFO - 训练基线分类器...
2025-06-26 17:20:54,348 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 17:20:54,348 - __main__ - INFO - 开始训练分类器...
2025-06-26 17:20:54,615 - __main__ - INFO - Epoch   1/10 | Train Loss: 2.0765 Acc: 0.1250 | Val Loss: 2.0801 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓
2025-06-26 17:20:54,665 - __main__ - INFO - Epoch   2/10 | Train Loss: 2.0783 Acc: 0.2500 | Val Loss: 2.0811 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:20:54,716 - __main__ - INFO - Epoch   3/10 | Train Loss: 2.0645 Acc: 0.1875 | Val Loss: 2.0824 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:20:54,761 - __main__ - INFO - Epoch   4/10 | Train Loss: 2.0663 Acc: 0.3125 | Val Loss: 2.0841 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:20:54,808 - __main__ - INFO - Epoch   5/10 | Train Loss: 2.0646 Acc: 0.0625 | Val Loss: 2.0861 Acc: 0.1250 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:20:54,858 - __main__ - INFO - Epoch   6/10 | Train Loss: 2.0490 Acc: 0.2500 | Val Loss: 2.0884 Acc: 0.1250 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:20:54,905 - __main__ - INFO - Epoch   7/10 | Train Loss: 2.0466 Acc: 0.2500 | Val Loss: 2.0910 Acc: 0.0000 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:20:54,952 - __main__ - INFO - Epoch   8/10 | Train Loss: 2.0383 Acc: 0.3125 | Val Loss: 2.0938 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:20:55,000 - __main__ - INFO - Epoch   9/10 | Train Loss: 2.0364 Acc: 0.2500 | Val Loss: 2.0970 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:20:55,050 - __main__ - INFO - Epoch  10/10 | Train Loss: 2.0278 Acc: 0.3750 | Val Loss: 2.1006 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:20:55,050 - __main__ - INFO - 分类器训练完成，用时: 00:00
2025-06-26 17:20:55,050 - __main__ - INFO - 最佳验证准确率: 0.1250
2025-06-26 17:20:55,051 - __main__ - INFO - 🔄 使用预训练扩散模型生成样本: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 17:20:55,051 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 17:20:55,051 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 17:20:55,051 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 17:20:55,051 - models.cddpm - INFO -   类别数量: 8
2025-06-26 17:20:55,197 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 17:20:55,197 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 17:20:55,197 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 17:20:55,198 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 17:20:55,450 - __main__ - INFO - 扩散模型已加载: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 17:20:55,451 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 1 个...
2025-06-26 17:20:55,451 - __main__ - INFO - 生成故障样本+健康样本，健康样本标签=0，每类生成1个
2025-06-26 17:20:55,451 - __main__ - INFO - 生成类别 0 的样本...
2025-06-26 17:21:11,841 - __main__ - INFO - 生成类别 1 的样本...
2025-06-26 17:21:28,716 - __main__ - INFO - 生成类别 2 的样本...
2025-06-26 17:21:45,659 - __main__ - INFO - 生成类别 3 的样本...
2025-06-26 17:22:02,725 - __main__ - INFO - 生成类别 4 的样本...
2025-06-26 17:22:19,611 - __main__ - INFO - 生成类别 5 的样本...
2025-06-26 17:22:38,161 - __main__ - INFO - 生成类别 6 的样本...
2025-06-26 17:22:55,805 - __main__ - INFO - 生成类别 7 的样本...
2025-06-26 17:23:13,296 - __main__ - INFO - 样本生成完成，总共生成 8 个样本
2025-06-26 17:23:13,297 - __main__ - INFO - 📊 数据分布分析:
2025-06-26 17:23:13,297 - __main__ - INFO -   原始数据类别分布: {0: 2, 1: 3, 2: 2, 3: 2, 4: 2, 5: 2, 6: 2, 7: 1}
2025-06-26 17:23:13,298 - __main__ - INFO -   生成数据类别分布: {0: 1, 1: 1, 2: 1, 3: 1, 4: 1, 5: 1, 6: 1, 7: 1}
2025-06-26 17:23:13,298 - __main__ - INFO -   增强数据类别分布: {0: 3, 1: 4, 2: 3, 3: 3, 4: 3, 5: 3, 6: 3, 7: 2}
2025-06-26 17:23:13,298 - __main__ - INFO - 🔍 数据质量检查:
2025-06-26 17:23:13,298 - __main__ - INFO -   原始数据形状: (16, 1024)
2025-06-26 17:23:13,298 - __main__ - INFO -   生成数据形状: (8, 1024)
2025-06-26 17:23:13,298 - __main__ - INFO -   增强数据形状: (24, 1024)
2025-06-26 17:23:13,298 - __main__ - INFO -   原始数据范围: [0.0000, 1.0000]
2025-06-26 17:23:13,298 - __main__ - INFO -   生成数据范围: [-207.4646, 163.2997]
2025-06-26 17:23:13,322 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 17:23:13,323 - __main__ - INFO - 开始训练分类器...
2025-06-26 17:23:13,418 - __main__ - INFO - Epoch   1/10 | Train Loss: 2.0840 Acc: 0.1250 | Val Loss: 2.0794 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓
2025-06-26 17:23:13,513 - __main__ - INFO - Epoch   2/10 | Train Loss: 2.0768 Acc: 0.1667 | Val Loss: 2.0798 Acc: 0.2500 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓
2025-06-26 17:23:13,568 - __main__ - INFO - Epoch   3/10 | Train Loss: 2.0653 Acc: 0.1667 | Val Loss: 2.0799 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:23:13,625 - __main__ - INFO - Epoch   4/10 | Train Loss: 2.0559 Acc: 0.1667 | Val Loss: 2.0803 Acc: 0.0000 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:23:13,685 - __main__ - INFO - Epoch   5/10 | Train Loss: 2.0619 Acc: 0.0833 | Val Loss: 2.0809 Acc: 0.0000 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:23:13,748 - __main__ - INFO - Epoch   6/10 | Train Loss: 2.0452 Acc: 0.1667 | Val Loss: 2.0818 Acc: 0.0000 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:23:13,805 - __main__ - INFO - Epoch   7/10 | Train Loss: 2.0369 Acc: 0.2083 | Val Loss: 2.0827 Acc: 0.0000 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:23:13,862 - __main__ - INFO - Epoch   8/10 | Train Loss: 2.0387 Acc: 0.1250 | Val Loss: 2.0837 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:23:13,920 - __main__ - INFO - Epoch   9/10 | Train Loss: 2.0276 Acc: 0.1667 | Val Loss: 2.0847 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:23:13,975 - __main__ - INFO - Epoch  10/10 | Train Loss: 2.0313 Acc: 0.1667 | Val Loss: 2.0858 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:23:13,976 - __main__ - INFO - 分类器训练完成，用时: 00:00
2025-06-26 17:23:13,977 - __main__ - INFO - 最佳验证准确率: 0.2500
2025-06-26 17:23:13,977 - __main__ - INFO - 评估模型性能...
2025-06-26 17:23:15,065 - __main__ - INFO - ================================================================================
2025-06-26 17:23:15,066 - __main__ - INFO - 📊 基准 vs 增强后性能对比分析
2025-06-26 17:23:15,066 - __main__ - INFO - ================================================================================
2025-06-26 17:23:15,066 - __main__ - INFO - 🔹 基准分类器性能（仅原始数据）:
2025-06-26 17:23:15,066 - __main__ - INFO -   准确率 (Accuracy): 0.1319
2025-06-26 17:23:15,067 - __main__ - INFO -   精确率 (Precision): 0.0252
2025-06-26 17:23:15,067 - __main__ - INFO -   召回率 (Recall): 0.1319
2025-06-26 17:23:15,067 - __main__ - INFO -   F1分数 (F1-Score): 0.0382
2025-06-26 17:23:15,067 - __main__ - INFO - 🔸 增强分类器性能（原始+生成数据）:
2025-06-26 17:23:15,067 - __main__ - INFO -   准确率 (Accuracy): 0.1249
2025-06-26 17:23:15,067 - __main__ - INFO -   精确率 (Precision): 0.0156
2025-06-26 17:23:15,067 - __main__ - INFO -   召回率 (Recall): 0.1249
2025-06-26 17:23:15,067 - __main__ - INFO -   F1分数 (F1-Score): 0.0277
2025-06-26 17:23:15,067 - __main__ - INFO - 📈 性能改进分析:
2025-06-26 17:23:15,067 - __main__ - INFO -   准确率改进: -0.0070 (-5.30%)
2025-06-26 17:23:15,067 - __main__ - INFO -   精确率改进: -0.0096 (-38.09%)
2025-06-26 17:23:15,067 - __main__ - INFO -   召回率改进: -0.0070 (-5.30%)
2025-06-26 17:23:15,067 - __main__ - INFO -   F1分数改进: -0.0105 (-27.41%)
2025-06-26 17:23:15,068 - __main__ - INFO - 📊 数据增强效果分析:
2025-06-26 17:23:15,068 - __main__ - INFO -   原始训练样本: 16
2025-06-26 17:23:15,068 - __main__ - INFO -   生成样本: 8
2025-06-26 17:23:15,068 - __main__ - INFO -   增强后总样本: 24
2025-06-26 17:23:15,068 - __main__ - INFO -   数据增长率: 50.0%
2025-06-26 17:23:15,068 - __main__ - INFO - ⚠️  数据增强效果不明显，可能需要调整参数
2025-06-26 17:23:15,068 - __main__ - INFO - ================================================================================
2025-06-26 17:23:15,081 - common.results_manager - INFO - 单次实验结果已保存: results\KAT\20250626_172034\individual_experiments\experiment_001
2025-06-26 17:23:15,082 - __main__ - INFO - 实验 1 完成
2025-06-26 17:23:15,082 - __main__ - INFO - ============================================================
2025-06-26 17:23:15,082 - __main__ - INFO - 开始实验 2/2 (组内 2/2)
2025-06-26 17:23:15,082 - __main__ - INFO - 实验参数: {'augmentation.num_generated_per_class': 2}
2025-06-26 17:23:15,082 - __main__ - INFO - 🔄 重用扩散模型: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 17:23:15,082 - __main__ - INFO - ============================================================
2025-06-26 17:23:15,093 - __main__ - INFO - 缓存配置已保存: cache\20250626_172034\experiment_002_config.yaml
2025-06-26 17:23:15,093 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 17:23:15,093 - common.data_loader - INFO - 数据类型: sequential, 样本选择方式: sequential
2025-06-26 17:23:15,098 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 17:23:15,103 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 17:23:15,104 - common.data_loader - INFO - 信号长度已匹配，无需截取: 1024
2025-06-26 17:23:15,104 - common.data_loader - INFO - 信号长度已匹配，无需截取: 1024
2025-06-26 17:23:15,104 - common.data_loader - INFO - 健康样本数量设置为-1，自动匹配故障样本数量: 3
2025-06-26 17:23:15,104 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 17:23:15,104 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 17:23:15,104 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:23:15,105 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:23:15,105 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:23:15,105 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:23:15,105 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:23:15,105 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:23:15,105 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 17:23:15,105 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:23:15,109 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 17:23:15,111 - common.data_loader - INFO - 数据加载完成:
2025-06-26 17:23:15,111 - common.data_loader - INFO -   训练样本: 24
2025-06-26 17:23:15,111 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 17:23:15,111 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 17:23:15,111 - common.data_loader - INFO -   类别数: 8
2025-06-26 17:23:15,112 - __main__ - INFO - 训练基线分类器...
2025-06-26 17:23:15,136 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 17:23:15,136 - __main__ - INFO - 开始训练分类器...
2025-06-26 17:23:15,241 - __main__ - INFO - Epoch   1/10 | Train Loss: 2.0765 Acc: 0.1250 | Val Loss: 2.0801 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓
2025-06-26 17:23:15,296 - __main__ - INFO - Epoch   2/10 | Train Loss: 2.0783 Acc: 0.2500 | Val Loss: 2.0811 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:23:15,345 - __main__ - INFO - Epoch   3/10 | Train Loss: 2.0645 Acc: 0.1875 | Val Loss: 2.0824 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:23:15,392 - __main__ - INFO - Epoch   4/10 | Train Loss: 2.0663 Acc: 0.3125 | Val Loss: 2.0841 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:23:15,442 - __main__ - INFO - Epoch   5/10 | Train Loss: 2.0646 Acc: 0.0625 | Val Loss: 2.0861 Acc: 0.1250 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:23:15,504 - __main__ - INFO - Epoch   6/10 | Train Loss: 2.0490 Acc: 0.2500 | Val Loss: 2.0884 Acc: 0.1250 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:23:15,551 - __main__ - INFO - Epoch   7/10 | Train Loss: 2.0466 Acc: 0.2500 | Val Loss: 2.0910 Acc: 0.0000 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:23:15,601 - __main__ - INFO - Epoch   8/10 | Train Loss: 2.0383 Acc: 0.3125 | Val Loss: 2.0938 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:23:15,648 - __main__ - INFO - Epoch   9/10 | Train Loss: 2.0364 Acc: 0.2500 | Val Loss: 2.0970 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:23:15,706 - __main__ - INFO - Epoch  10/10 | Train Loss: 2.0278 Acc: 0.3750 | Val Loss: 2.1006 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:23:15,707 - __main__ - INFO - 分类器训练完成，用时: 00:00
2025-06-26 17:23:15,707 - __main__ - INFO - 最佳验证准确率: 0.1250
2025-06-26 17:23:15,707 - __main__ - INFO - 🔄 使用预训练扩散模型生成样本: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 17:23:15,708 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 17:23:15,708 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 17:23:15,708 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 17:23:15,708 - models.cddpm - INFO -   类别数量: 8
2025-06-26 17:23:15,848 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 17:23:15,848 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 17:23:15,848 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 17:23:15,848 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 17:23:16,277 - __main__ - INFO - 扩散模型已加载: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 17:23:16,279 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 2 个...
2025-06-26 17:23:16,279 - __main__ - INFO - 生成故障样本+健康样本，健康样本标签=0，每类生成2个
2025-06-26 17:23:16,279 - __main__ - INFO - 生成类别 0 的样本...
2025-06-26 17:23:34,092 - __main__ - INFO - 生成类别 1 的样本...
2025-06-26 17:23:51,515 - __main__ - INFO - 生成类别 2 的样本...
2025-06-26 17:24:08,839 - __main__ - INFO - 生成类别 3 的样本...
2025-06-26 17:24:25,812 - __main__ - INFO - 生成类别 4 的样本...
2025-06-26 17:24:42,844 - __main__ - INFO - 生成类别 5 的样本...
2025-06-26 17:24:59,179 - __main__ - INFO - 生成类别 6 的样本...
2025-06-26 17:25:15,150 - __main__ - INFO - 生成类别 7 的样本...
2025-06-26 17:25:31,101 - __main__ - INFO - 样本生成完成，总共生成 16 个样本
2025-06-26 17:25:31,101 - __main__ - INFO - 📊 数据分布分析:
2025-06-26 17:25:31,101 - __main__ - INFO -   原始数据类别分布: {0: 2, 1: 3, 2: 2, 3: 2, 4: 2, 5: 2, 6: 2, 7: 1}
2025-06-26 17:25:31,101 - __main__ - INFO -   生成数据类别分布: {0: 2, 1: 2, 2: 2, 3: 2, 4: 2, 5: 2, 6: 2, 7: 2}
2025-06-26 17:25:31,101 - __main__ - INFO -   增强数据类别分布: {0: 4, 1: 5, 2: 4, 3: 4, 4: 4, 5: 4, 6: 4, 7: 3}
2025-06-26 17:25:31,101 - __main__ - INFO - 🔍 数据质量检查:
2025-06-26 17:25:31,101 - __main__ - INFO -   原始数据形状: (16, 1024)
2025-06-26 17:25:31,101 - __main__ - INFO -   生成数据形状: (16, 1024)
2025-06-26 17:25:31,101 - __main__ - INFO -   增强数据形状: (32, 1024)
2025-06-26 17:25:31,101 - __main__ - INFO -   原始数据范围: [0.0000, 1.0000]
2025-06-26 17:25:31,101 - __main__ - INFO -   生成数据范围: [-207.4651, 163.3009]
2025-06-26 17:25:31,132 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 17:25:31,133 - __main__ - INFO - 开始训练分类器...
2025-06-26 17:25:31,258 - __main__ - INFO - Epoch   1/10 | Train Loss: 2.0764 Acc: 0.1562 | Val Loss: 2.0793 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓
2025-06-26 17:25:31,324 - __main__ - INFO - Epoch   2/10 | Train Loss: 2.0731 Acc: 0.1562 | Val Loss: 2.0795 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:25:31,387 - __main__ - INFO - Epoch   3/10 | Train Loss: 2.0580 Acc: 0.2500 | Val Loss: 2.0796 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:25:31,455 - __main__ - INFO - Epoch   4/10 | Train Loss: 2.0486 Acc: 0.2812 | Val Loss: 2.0801 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:25:31,520 - __main__ - INFO - Epoch   5/10 | Train Loss: 2.0393 Acc: 0.2188 | Val Loss: 2.0807 Acc: 0.0000 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:25:31,587 - __main__ - INFO - Epoch   6/10 | Train Loss: 2.0259 Acc: 0.2188 | Val Loss: 2.0815 Acc: 0.0000 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:25:31,655 - __main__ - INFO - Epoch   7/10 | Train Loss: 2.0134 Acc: 0.1875 | Val Loss: 2.0825 Acc: 0.0000 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:25:31,719 - __main__ - INFO - Epoch   8/10 | Train Loss: 2.0106 Acc: 0.2188 | Val Loss: 2.0837 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:25:31,783 - __main__ - INFO - Epoch   9/10 | Train Loss: 1.9972 Acc: 0.1875 | Val Loss: 2.0850 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:25:31,847 - __main__ - INFO - Epoch  10/10 | Train Loss: 1.9992 Acc: 0.1875 | Val Loss: 2.0864 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:25:31,848 - __main__ - INFO - 分类器训练完成，用时: 00:00
2025-06-26 17:25:31,848 - __main__ - INFO - 最佳验证准确率: 0.1250
2025-06-26 17:25:31,848 - __main__ - INFO - 评估模型性能...
2025-06-26 17:25:32,857 - __main__ - INFO - ================================================================================
2025-06-26 17:25:32,857 - __main__ - INFO - 📊 基准 vs 增强后性能对比分析
2025-06-26 17:25:32,857 - __main__ - INFO - ================================================================================
2025-06-26 17:25:32,857 - __main__ - INFO - 🔹 基准分类器性能（仅原始数据）:
2025-06-26 17:25:32,857 - __main__ - INFO -   准确率 (Accuracy): 0.1319
2025-06-26 17:25:32,857 - __main__ - INFO -   精确率 (Precision): 0.0252
2025-06-26 17:25:32,858 - __main__ - INFO -   召回率 (Recall): 0.1319
2025-06-26 17:25:32,858 - __main__ - INFO -   F1分数 (F1-Score): 0.0382
2025-06-26 17:25:32,858 - __main__ - INFO - 🔸 增强分类器性能（原始+生成数据）:
2025-06-26 17:25:32,858 - __main__ - INFO -   准确率 (Accuracy): 0.1249
2025-06-26 17:25:32,858 - __main__ - INFO -   精确率 (Precision): 0.0156
2025-06-26 17:25:32,858 - __main__ - INFO -   召回率 (Recall): 0.1249
2025-06-26 17:25:32,858 - __main__ - INFO -   F1分数 (F1-Score): 0.0277
2025-06-26 17:25:32,858 - __main__ - INFO - 📈 性能改进分析:
2025-06-26 17:25:32,859 - __main__ - INFO -   准确率改进: -0.0070 (-5.30%)
2025-06-26 17:25:32,859 - __main__ - INFO -   精确率改进: -0.0096 (-38.09%)
2025-06-26 17:25:32,859 - __main__ - INFO -   召回率改进: -0.0070 (-5.30%)
2025-06-26 17:25:32,859 - __main__ - INFO -   F1分数改进: -0.0105 (-27.41%)
2025-06-26 17:25:32,859 - __main__ - INFO - 📊 数据增强效果分析:
2025-06-26 17:25:32,859 - __main__ - INFO -   原始训练样本: 16
2025-06-26 17:25:32,859 - __main__ - INFO -   生成样本: 16
2025-06-26 17:25:32,859 - __main__ - INFO -   增强后总样本: 32
2025-06-26 17:25:32,859 - __main__ - INFO -   数据增长率: 100.0%
2025-06-26 17:25:32,860 - __main__ - INFO - ⚠️  数据增强效果不明显，可能需要调整参数
2025-06-26 17:25:32,860 - __main__ - INFO - ================================================================================
2025-06-26 17:25:32,871 - common.results_manager - INFO - 单次实验结果已保存: results\KAT\20250626_172034\individual_experiments\experiment_002
2025-06-26 17:25:32,871 - __main__ - INFO - 实验 2 完成
2025-06-26 17:25:32,882 - common.experiment_manager - INFO - 对比实验结果汇总已保存: results\KAT\20250626_172034\comparison_summary\comparison_results_summary.csv
2025-06-26 17:25:32,882 - common.experiment_manager - INFO - ================================================================================
2025-06-26 17:25:32,882 - common.experiment_manager - INFO - 对比实验结果汇总
2025-06-26 17:25:32,882 - common.experiment_manager - INFO - ================================================================================
2025-06-26 17:25:32,885 - common.experiment_manager - INFO - 
数据集: KAT
2025-06-26 17:25:32,885 - common.experiment_manager - INFO - ----------------------------------------
2025-06-26 17:25:32,886 - common.experiment_manager - INFO - 实验  1: 方法=CDDPM    | 样本=N/A | 生成=  1 | 准确率=0.0000 | 提升=+0.0000
2025-06-26 17:25:32,886 - common.experiment_manager - INFO - 实验  2: 方法=CDDPM    | 样本=N/A | 生成=  2 | 准确率=0.0000 | 提升=+0.0000
2025-06-26 17:25:32,896 - common.results_manager - INFO - 对比实验汇总已保存: results\KAT\20250626_172034\comparison_summary\comparison_summary.csv
2025-06-26 17:25:32,896 - __main__ - INFO - ================================================================================
2025-06-26 17:25:32,897 - __main__ - INFO - 🎉 智能重用对比实验全部完成，总用时: 04:58
2025-06-26 17:25:32,897 - __main__ - INFO - 💾 扩散模型重用次数: 1
2025-06-26 17:25:32,897 - __main__ - INFO - ⏱️ 预计节省时间: 约 0.0 倍扩散模型训练时间
2025-06-26 17:25:32,897 - __main__ - INFO - 结果保存在: results\KAT\20250626_172034
2025-06-26 17:25:32,897 - __main__ - INFO - 缓存配置保存在: cache\20250626_172034
2025-06-26 17:25:32,897 - __main__ - INFO - ================================================================================
2025-06-26 17:25:32,897 - __main__ - INFO - 实验完成
2025-06-26 17:25:32,897 - __main__ - INFO - 程序结束
