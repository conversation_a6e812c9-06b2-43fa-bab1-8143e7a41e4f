2025-06-26 17:20:34,516 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\cddpm_fault_diagnosis_comparison_20250626_172034_20250626_172034.log
2025-06-26 17:20:34,516 - common.experiment_manager - INFO - 检测到只有生成数量参数变化，将重用扩散模型
2025-06-26 17:20:34,517 - __main__ - INFO - ================================================================================
2025-06-26 17:20:34,517 - __main__ - INFO - 检测到对比实验配置
2025-06-26 17:20:34,517 - __main__ - INFO - 实验名称: cddpm_fault_diagnosis
2025-06-26 17:20:34,517 - __main__ - INFO - 数据集: KAT
2025-06-26 17:20:34,517 - __main__ - INFO - 总实验数: 2
2025-06-26 17:20:34,517 - __main__ - INFO - 对比参数: ['augmentation.num_generated_per_class']
2025-06-26 17:20:34,517 - __main__ - INFO - 🔥 扩散模型重用: 是
2025-06-26 17:20:34,517 - __main__ - INFO - ================================================================================
2025-06-26 17:20:34,518 - __main__ - INFO - 缓存配置文件保存在: cache\20250626_172034
2025-06-26 17:20:34,518 - common.results_manager - INFO - 原始配置文件已复制到结果目录: results\KAT\20250626_172034\configs\original_config.yaml
2025-06-26 17:20:34,518 - __main__ - INFO - 🔥 启用扩散模型重用模式
2025-06-26 17:20:34,519 - common.experiment_manager - INFO - 实验分组完成，共 1 个训练数据组
2025-06-26 17:20:34,519 - common.experiment_manager - INFO -   组 1: 2 个实验 - dataset=KAT_fault=3_healthy=-1_length=1024
2025-06-26 17:20:34,519 - __main__ - INFO - ======================================================================
2025-06-26 17:20:34,519 - __main__ - INFO - 处理实验组: dataset=KAT_fault=3_healthy=-1_length=1024
2025-06-26 17:20:34,519 - __main__ - INFO - 该组包含 2 个实验
2025-06-26 17:20:34,519 - __main__ - INFO - ======================================================================
2025-06-26 17:20:34,519 - __main__ - INFO - 🚀 训练扩散模型（该组唯一一次）...
2025-06-26 17:20:34,519 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 17:20:34,519 - common.data_loader - INFO - 数据类型: sequential, 样本选择方式: sequential
2025-06-26 17:20:34,524 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 17:20:34,530 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 17:20:34,530 - common.data_loader - INFO - 信号长度已匹配，无需截取: 1024
2025-06-26 17:20:34,530 - common.data_loader - INFO - 信号长度已匹配，无需截取: 1024
2025-06-26 17:20:34,530 - common.data_loader - INFO - 健康样本数量设置为-1，自动匹配故障样本数量: 3
2025-06-26 17:20:34,530 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 17:20:34,530 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 17:20:34,530 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:34,530 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:34,530 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:34,530 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:34,530 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:34,530 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:34,531 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 17:20:34,531 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:20:34,535 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 17:20:34,539 - common.data_loader - INFO - 数据加载完成:
2025-06-26 17:20:34,539 - common.data_loader - INFO -   训练样本: 24
2025-06-26 17:20:34,539 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 17:20:34,539 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 17:20:34,539 - common.data_loader - INFO -   类别数: 8
2025-06-26 17:20:34,540 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 17:20:34,540 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 17:20:34,540 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 17:20:34,540 - models.cddpm - INFO -   类别数量: 8
2025-06-26 17:20:34,712 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 17:20:34,712 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 17:20:34,712 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 17:20:34,712 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 17:20:34,860 - __main__ - INFO - 启用channels_last内存格式
2025-06-26 17:20:34,861 - __main__ - INFO - 启用自动混合精度训练
2025-06-26 17:20:34,862 - __main__ - INFO - 扩散模型参数数量: 44,324,929
2025-06-26 17:20:34,862 - __main__ - INFO - 开始训练扩散模型...
2025-06-26 17:20:34,862 - __main__ - INFO - 最佳模型判断标准: weighted_loss (min)
2025-06-26 17:20:34,862 - __main__ - INFO - 加权损失配置: 训练损失权重=0.7, 验证损失权重=0.3
