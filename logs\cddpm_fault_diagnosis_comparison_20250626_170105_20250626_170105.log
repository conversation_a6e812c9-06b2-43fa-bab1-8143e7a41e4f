2025-06-26 17:01:05,159 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\cddpm_fault_diagnosis_comparison_20250626_170105_20250626_170105.log
2025-06-26 17:01:05,159 - common.experiment_manager - INFO - 检测到只有生成数量参数变化，将重用扩散模型
2025-06-26 17:01:05,159 - __main__ - INFO - ================================================================================
2025-06-26 17:01:05,159 - __main__ - INFO - 检测到对比实验配置
2025-06-26 17:01:05,159 - __main__ - INFO - 实验名称: cddpm_fault_diagnosis
2025-06-26 17:01:05,159 - __main__ - INFO - 数据集: KAT
2025-06-26 17:01:05,159 - __main__ - INFO - 总实验数: 2
2025-06-26 17:01:05,159 - __main__ - INFO - 对比参数: ['augmentation.num_generated_per_class']
2025-06-26 17:01:05,159 - __main__ - INFO - 🔥 扩散模型重用: 是
2025-06-26 17:01:05,160 - __main__ - INFO - ================================================================================
2025-06-26 17:01:05,160 - __main__ - INFO - 缓存配置文件保存在: cache\20250626_170105
2025-06-26 17:01:05,161 - common.results_manager - INFO - 原始配置文件已复制到结果目录: results\KAT\20250626_170105\configs\original_config.yaml
2025-06-26 17:01:05,161 - __main__ - INFO - 🔥 启用扩散模型重用模式
2025-06-26 17:01:05,161 - common.experiment_manager - INFO - 实验分组完成，共 1 个训练数据组
2025-06-26 17:01:05,161 - common.experiment_manager - INFO -   组 1: 2 个实验 - dataset=KAT_fault=3_healthy=-1_length=1024
2025-06-26 17:01:05,161 - __main__ - INFO - ======================================================================
2025-06-26 17:01:05,161 - __main__ - INFO - 处理实验组: dataset=KAT_fault=3_healthy=-1_length=1024
2025-06-26 17:01:05,161 - __main__ - INFO - 该组包含 2 个实验
2025-06-26 17:01:05,161 - __main__ - INFO - ======================================================================
2025-06-26 17:01:05,161 - __main__ - INFO - 🚀 训练扩散模型（该组唯一一次）...
2025-06-26 17:01:05,162 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 17:01:05,162 - common.data_loader - INFO - 数据类型: sequential, 样本选择方式: sequential
2025-06-26 17:01:05,167 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 17:01:05,172 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 17:01:05,172 - common.data_loader - INFO - 信号长度已匹配，无需截取: 1024
2025-06-26 17:01:05,172 - common.data_loader - INFO - 信号长度已匹配，无需截取: 1024
2025-06-26 17:01:05,172 - common.data_loader - INFO - 健康样本数量设置为-1，自动匹配故障样本数量: 3
2025-06-26 17:01:05,172 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 17:01:05,173 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 17:01:05,173 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:01:05,173 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:01:05,173 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:01:05,173 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:01:05,174 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:01:05,174 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:01:05,174 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 17:01:05,174 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:01:05,177 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 17:01:05,184 - common.data_loader - INFO - 数据加载完成:
2025-06-26 17:01:05,184 - common.data_loader - INFO -   训练样本: 24
2025-06-26 17:01:05,184 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 17:01:05,184 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 17:01:05,184 - common.data_loader - INFO -   类别数: 8
2025-06-26 17:01:05,185 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 17:01:05,186 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 17:01:05,186 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 17:01:05,186 - models.cddpm - INFO -   类别数量: 8
2025-06-26 17:01:05,375 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 17:01:05,375 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 17:01:05,375 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 17:01:05,378 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 17:01:05,559 - __main__ - INFO - 启用channels_last内存格式
2025-06-26 17:01:05,560 - __main__ - INFO - 启用自动混合精度训练
2025-06-26 17:01:05,560 - __main__ - INFO - 扩散模型参数数量: 44,324,929
2025-06-26 17:01:05,561 - __main__ - INFO - 开始训练扩散模型...
2025-06-26 17:01:05,561 - __main__ - INFO - 最佳模型判断标准: weighted_loss (min)
2025-06-26 17:01:05,561 - __main__ - INFO - 加权损失配置: 训练损失权重=0.7, 验证损失权重=0.3
2025-06-26 17:01:15,619 - __main__ - INFO - Epoch   1/20 | Train Loss: 0.821613 | Val Loss: 0.744884 | Weighted Loss: 0.798594 | LR: 1.00e-04 | Time: 00:08 | Save: Best✓(weighted_loss=0.798594)
2025-06-26 17:01:16,478 - __main__ - INFO - Epoch   2/20 | Train Loss: 0.776075 | Val Loss: 0.745829 | Weighted Loss: 0.767002 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.767002)
2025-06-26 17:01:17,210 - __main__ - INFO - Epoch   3/20 | Train Loss: 0.753688 | Val Loss: 0.727417 | Weighted Loss: 0.745807 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.745807)
2025-06-26 17:01:17,957 - __main__ - INFO - Epoch   4/20 | Train Loss: 0.749071 | Val Loss: 0.673177 | Weighted Loss: 0.726302 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.726302)
2025-06-26 17:01:18,759 - __main__ - INFO - Epoch   5/20 | Train Loss: 0.722245 | Val Loss: 0.662566 | Weighted Loss: 0.704341 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.704341)
2025-06-26 17:01:19,479 - __main__ - INFO - Epoch   6/20 | Train Loss: 0.703136 | Val Loss: 0.683199 | Weighted Loss: 0.697155 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.697155)
2025-06-26 17:01:20,174 - __main__ - INFO - Epoch   7/20 | Train Loss: 0.668809 | Val Loss: 0.605941 | Weighted Loss: 0.649949 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.649949)
2025-06-26 17:01:20,880 - __main__ - INFO - Epoch   8/20 | Train Loss: 0.682044 | Val Loss: 0.557653 | Weighted Loss: 0.644727 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.644727)
2025-06-26 17:01:21,617 - __main__ - INFO - Epoch   9/20 | Train Loss: 0.648346 | Val Loss: 0.574968 | Weighted Loss: 0.626333 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.626333)
2025-06-26 17:01:22,330 - __main__ - INFO - Epoch  10/20 | Train Loss: 0.590509 | Val Loss: 0.541598 | Weighted Loss: 0.575836 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.575836)
2025-06-26 17:01:23,057 - __main__ - INFO - Epoch  11/20 | Train Loss: 0.590852 | Val Loss: 0.489901 | Weighted Loss: 0.560567 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.560567)
2025-06-26 17:01:23,326 - __main__ - INFO - Epoch  12/20 | Train Loss: 0.599600 | Val Loss: 0.487087 | Weighted Loss: 0.565846 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:01:24,050 - __main__ - INFO - Epoch  13/20 | Train Loss: 0.536536 | Val Loss: 0.494332 | Weighted Loss: 0.523875 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.523875)
2025-06-26 17:01:24,330 - __main__ - INFO - Epoch  14/20 | Train Loss: 0.562335 | Val Loss: 0.508708 | Weighted Loss: 0.546247 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:01:25,049 - __main__ - INFO - Epoch  15/20 | Train Loss: 0.518021 | Val Loss: 0.460514 | Weighted Loss: 0.500769 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.500769)
2025-06-26 17:01:25,330 - __main__ - INFO - Epoch  16/20 | Train Loss: 0.532066 | Val Loss: 0.461141 | Weighted Loss: 0.510788 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:01:26,046 - __main__ - INFO - Epoch  17/20 | Train Loss: 0.473458 | Val Loss: 0.378048 | Weighted Loss: 0.444835 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.444835)
2025-06-26 17:01:26,985 - __main__ - INFO - Epoch  18/20 | Train Loss: 0.384269 | Val Loss: 0.349678 | Weighted Loss: 0.373892 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.373892)
2025-06-26 17:01:27,266 - __main__ - INFO - Epoch  19/20 | Train Loss: 0.469238 | Val Loss: 0.299757 | Weighted Loss: 0.418394 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:01:27,986 - __main__ - INFO - Epoch  20/20 | Train Loss: 0.361507 | Val Loss: 0.275725 | Weighted Loss: 0.335772 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓(weighted_loss=0.335772)
2025-06-26 17:01:27,987 - __main__ - INFO - 扩散模型训练完成，用时: 00:22
2025-06-26 17:01:27,987 - __main__ - INFO - 最佳验证损失: 0.275725
2025-06-26 17:01:27,987 - __main__ - INFO - ✅ 扩散模型训练完成，保存至: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 17:01:27,988 - __main__ - INFO - ============================================================
2025-06-26 17:01:27,989 - __main__ - INFO - 开始实验 1/2 (组内 1/2)
2025-06-26 17:01:27,989 - __main__ - INFO - 实验参数: {'augmentation.num_generated_per_class': 5}
2025-06-26 17:01:27,989 - __main__ - INFO - 🔄 重用扩散模型: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 17:01:27,989 - __main__ - INFO - ============================================================
2025-06-26 17:01:28,000 - __main__ - INFO - 缓存配置已保存: cache\20250626_170105\experiment_001_config.yaml
2025-06-26 17:01:28,001 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 17:01:28,001 - common.data_loader - INFO - 数据类型: sequential, 样本选择方式: sequential
2025-06-26 17:01:28,008 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 17:01:28,014 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 17:01:28,014 - common.data_loader - INFO - 信号长度已匹配，无需截取: 1024
2025-06-26 17:01:28,014 - common.data_loader - INFO - 信号长度已匹配，无需截取: 1024
2025-06-26 17:01:28,014 - common.data_loader - INFO - 健康样本数量设置为-1，自动匹配故障样本数量: 3
2025-06-26 17:01:28,014 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 17:01:28,014 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 17:01:28,015 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:01:28,015 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:01:28,015 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:01:28,015 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:01:28,015 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:01:28,016 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:01:28,016 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 17:01:28,016 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:01:28,021 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 17:01:28,022 - common.data_loader - INFO - 数据加载完成:
2025-06-26 17:01:28,023 - common.data_loader - INFO -   训练样本: 24
2025-06-26 17:01:28,023 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 17:01:28,023 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 17:01:28,023 - common.data_loader - INFO -   类别数: 8
2025-06-26 17:01:28,024 - __main__ - INFO - 训练基线分类器...
2025-06-26 17:01:28,061 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 17:01:28,061 - __main__ - INFO - 开始训练分类器...
2025-06-26 17:01:28,326 - __main__ - INFO - Epoch   1/10 | Train Loss: 2.0765 Acc: 0.1250 | Val Loss: 2.0801 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓
2025-06-26 17:01:28,375 - __main__ - INFO - Epoch   2/10 | Train Loss: 2.0783 Acc: 0.2500 | Val Loss: 2.0811 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:01:28,424 - __main__ - INFO - Epoch   3/10 | Train Loss: 2.0645 Acc: 0.1875 | Val Loss: 2.0824 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:01:28,471 - __main__ - INFO - Epoch   4/10 | Train Loss: 2.0663 Acc: 0.3125 | Val Loss: 2.0841 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:01:28,516 - __main__ - INFO - Epoch   5/10 | Train Loss: 2.0646 Acc: 0.0625 | Val Loss: 2.0861 Acc: 0.1250 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:01:28,573 - __main__ - INFO - Epoch   6/10 | Train Loss: 2.0490 Acc: 0.2500 | Val Loss: 2.0884 Acc: 0.1250 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:01:28,622 - __main__ - INFO - Epoch   7/10 | Train Loss: 2.0466 Acc: 0.2500 | Val Loss: 2.0910 Acc: 0.0000 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:01:28,680 - __main__ - INFO - Epoch   8/10 | Train Loss: 2.0383 Acc: 0.3125 | Val Loss: 2.0938 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:01:28,727 - __main__ - INFO - Epoch   9/10 | Train Loss: 2.0364 Acc: 0.2500 | Val Loss: 2.0970 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:01:28,785 - __main__ - INFO - Epoch  10/10 | Train Loss: 2.0278 Acc: 0.3750 | Val Loss: 2.1006 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:01:28,785 - __main__ - INFO - 分类器训练完成，用时: 00:00
2025-06-26 17:01:28,786 - __main__ - INFO - 最佳验证准确率: 0.1250
2025-06-26 17:01:28,786 - __main__ - INFO - 🔄 使用预训练扩散模型生成样本: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 17:01:28,786 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 17:01:28,786 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 17:01:28,786 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 17:01:28,786 - models.cddpm - INFO -   类别数量: 8
2025-06-26 17:01:28,925 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 17:01:28,925 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 17:01:28,926 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 17:01:28,926 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 17:01:29,218 - __main__ - INFO - 扩散模型已加载: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 17:01:29,219 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 5 个...
2025-06-26 17:01:29,220 - __main__ - INFO - 生成故障样本+健康样本，健康样本标签=0，每类生成5个
2025-06-26 17:01:29,220 - __main__ - INFO - 生成类别 0 的样本...
2025-06-26 17:01:46,809 - __main__ - INFO - 生成类别 1 的样本...
2025-06-26 17:02:03,993 - __main__ - INFO - 生成类别 2 的样本...
2025-06-26 17:02:21,077 - __main__ - INFO - 生成类别 3 的样本...
2025-06-26 17:02:38,393 - __main__ - INFO - 生成类别 4 的样本...
2025-06-26 17:02:56,014 - __main__ - INFO - 生成类别 5 的样本...
2025-06-26 17:03:13,567 - __main__ - INFO - 生成类别 6 的样本...
2025-06-26 17:03:31,145 - __main__ - INFO - 生成类别 7 的样本...
2025-06-26 17:03:49,004 - __main__ - INFO - 样本生成完成，总共生成 40 个样本
2025-06-26 17:03:49,028 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 17:03:49,028 - __main__ - INFO - 开始训练分类器...
2025-06-26 17:03:49,177 - __main__ - INFO - Epoch   1/10 | Train Loss: 2.0865 Acc: 0.1071 | Val Loss: 2.0794 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓
2025-06-26 17:03:49,275 - __main__ - INFO - Epoch   2/10 | Train Loss: 2.0669 Acc: 0.1786 | Val Loss: 2.0793 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:03:49,373 - __main__ - INFO - Epoch   3/10 | Train Loss: 2.0516 Acc: 0.2321 | Val Loss: 2.0793 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:03:49,470 - __main__ - INFO - Epoch   4/10 | Train Loss: 2.0317 Acc: 0.1964 | Val Loss: 2.0798 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:03:49,567 - __main__ - INFO - Epoch   5/10 | Train Loss: 2.0185 Acc: 0.2143 | Val Loss: 2.0804 Acc: 0.0000 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:03:49,664 - __main__ - INFO - Epoch   6/10 | Train Loss: 2.0019 Acc: 0.1964 | Val Loss: 2.0812 Acc: 0.0000 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:03:49,760 - __main__ - INFO - Epoch   7/10 | Train Loss: 1.9861 Acc: 0.2143 | Val Loss: 2.0822 Acc: 0.0000 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:03:49,858 - __main__ - INFO - Epoch   8/10 | Train Loss: 1.9715 Acc: 0.3214 | Val Loss: 2.0832 Acc: 0.1250 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:03:49,956 - __main__ - INFO - Epoch   9/10 | Train Loss: 1.9614 Acc: 0.2321 | Val Loss: 2.0844 Acc: 0.1250 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:03:50,053 - __main__ - INFO - Epoch  10/10 | Train Loss: 1.9462 Acc: 0.1786 | Val Loss: 2.0857 Acc: 0.1250 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:03:50,054 - __main__ - INFO - 分类器训练完成，用时: 00:01
2025-06-26 17:03:50,054 - __main__ - INFO - 最佳验证准确率: 0.1250
2025-06-26 17:03:50,054 - __main__ - INFO - 评估模型性能...
2025-06-26 17:03:51,082 - common.results_manager - INFO - 单次实验结果已保存: results\KAT\20250626_170105\individual_experiments\experiment_001
2025-06-26 17:03:51,083 - __main__ - INFO - 实验 1 完成
2025-06-26 17:03:51,083 - __main__ - INFO - ============================================================
2025-06-26 17:03:51,083 - __main__ - INFO - 开始实验 2/2 (组内 2/2)
2025-06-26 17:03:51,083 - __main__ - INFO - 实验参数: {'augmentation.num_generated_per_class': 10}
2025-06-26 17:03:51,083 - __main__ - INFO - 🔄 重用扩散模型: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 17:03:51,083 - __main__ - INFO - ============================================================
2025-06-26 17:03:51,093 - __main__ - INFO - 缓存配置已保存: cache\20250626_170105\experiment_002_config.yaml
2025-06-26 17:03:51,093 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 17:03:51,093 - common.data_loader - INFO - 数据类型: sequential, 样本选择方式: sequential
2025-06-26 17:03:51,096 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 17:03:51,098 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 17:03:51,098 - common.data_loader - INFO - 信号长度已匹配，无需截取: 1024
2025-06-26 17:03:51,098 - common.data_loader - INFO - 信号长度已匹配，无需截取: 1024
2025-06-26 17:03:51,098 - common.data_loader - INFO - 健康样本数量设置为-1，自动匹配故障样本数量: 3
2025-06-26 17:03:51,098 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 17:03:51,099 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 17:03:51,099 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:03:51,099 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:03:51,099 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:03:51,099 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:03:51,099 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:03:51,099 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:03:51,100 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 17:03:51,100 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 17:03:51,103 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 17:03:51,104 - common.data_loader - INFO - 数据加载完成:
2025-06-26 17:03:51,105 - common.data_loader - INFO -   训练样本: 24
2025-06-26 17:03:51,105 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 17:03:51,105 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 17:03:51,105 - common.data_loader - INFO -   类别数: 8
2025-06-26 17:03:51,106 - __main__ - INFO - 训练基线分类器...
2025-06-26 17:03:51,129 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 17:03:51,129 - __main__ - INFO - 开始训练分类器...
2025-06-26 17:03:51,221 - __main__ - INFO - Epoch   1/10 | Train Loss: 2.0765 Acc: 0.1250 | Val Loss: 2.0801 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓
2025-06-26 17:03:51,275 - __main__ - INFO - Epoch   2/10 | Train Loss: 2.0783 Acc: 0.2500 | Val Loss: 2.0811 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:03:51,324 - __main__ - INFO - Epoch   3/10 | Train Loss: 2.0645 Acc: 0.1875 | Val Loss: 2.0824 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:03:51,371 - __main__ - INFO - Epoch   4/10 | Train Loss: 2.0663 Acc: 0.3125 | Val Loss: 2.0841 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:03:51,416 - __main__ - INFO - Epoch   5/10 | Train Loss: 2.0646 Acc: 0.0625 | Val Loss: 2.0861 Acc: 0.1250 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:03:51,476 - __main__ - INFO - Epoch   6/10 | Train Loss: 2.0490 Acc: 0.2500 | Val Loss: 2.0884 Acc: 0.1250 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:03:51,532 - __main__ - INFO - Epoch   7/10 | Train Loss: 2.0466 Acc: 0.2500 | Val Loss: 2.0910 Acc: 0.0000 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:03:51,579 - __main__ - INFO - Epoch   8/10 | Train Loss: 2.0383 Acc: 0.3125 | Val Loss: 2.0938 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:03:51,637 - __main__ - INFO - Epoch   9/10 | Train Loss: 2.0364 Acc: 0.2500 | Val Loss: 2.0970 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:03:51,703 - __main__ - INFO - Epoch  10/10 | Train Loss: 2.0278 Acc: 0.3750 | Val Loss: 2.1006 Acc: 0.0000 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:03:51,703 - __main__ - INFO - 分类器训练完成，用时: 00:00
2025-06-26 17:03:51,704 - __main__ - INFO - 最佳验证准确率: 0.1250
2025-06-26 17:03:51,704 - __main__ - INFO - 🔄 使用预训练扩散模型生成样本: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 17:03:51,704 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 17:03:51,705 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 17:03:51,705 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 17:03:51,705 - models.cddpm - INFO -   类别数量: 8
2025-06-26 17:03:51,838 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 17:03:51,838 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 17:03:51,838 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 17:03:51,839 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 17:03:52,093 - __main__ - INFO - 扩散模型已加载: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 17:03:52,094 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 10 个...
2025-06-26 17:03:52,095 - __main__ - INFO - 生成故障样本+健康样本，健康样本标签=0，每类生成10个
2025-06-26 17:03:52,095 - __main__ - INFO - 生成类别 0 的样本...
2025-06-26 17:04:15,899 - __main__ - INFO - 生成类别 1 的样本...
2025-06-26 17:04:39,558 - __main__ - INFO - 生成类别 2 的样本...
2025-06-26 17:05:03,475 - __main__ - INFO - 生成类别 3 的样本...
2025-06-26 17:05:27,601 - __main__ - INFO - 生成类别 4 的样本...
2025-06-26 17:05:51,446 - __main__ - INFO - 生成类别 5 的样本...
2025-06-26 17:06:15,514 - __main__ - INFO - 生成类别 6 的样本...
2025-06-26 17:06:39,240 - __main__ - INFO - 生成类别 7 的样本...
2025-06-26 17:07:03,108 - __main__ - INFO - 样本生成完成，总共生成 80 个样本
2025-06-26 17:07:03,133 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 17:07:03,134 - __main__ - INFO - 开始训练分类器...
2025-06-26 17:07:03,339 - __main__ - INFO - Epoch   1/10 | Train Loss: 2.0833 Acc: 0.1771 | Val Loss: 2.0794 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓
2025-06-26 17:07:03,502 - __main__ - INFO - Epoch   2/10 | Train Loss: 2.0670 Acc: 0.2188 | Val Loss: 2.0792 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:07:03,663 - __main__ - INFO - Epoch   3/10 | Train Loss: 2.0479 Acc: 0.2083 | Val Loss: 2.0792 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:07:03,819 - __main__ - INFO - Epoch   4/10 | Train Loss: 2.0303 Acc: 0.2500 | Val Loss: 2.0795 Acc: 0.1250 | LR: 1.00e-04 | Time: 00:00
2025-06-26 17:07:03,979 - __main__ - INFO - Epoch   5/10 | Train Loss: 2.0103 Acc: 0.2708 | Val Loss: 2.0799 Acc: 0.1250 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:07:04,140 - __main__ - INFO - Epoch   6/10 | Train Loss: 1.9933 Acc: 0.2708 | Val Loss: 2.0803 Acc: 0.1250 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:07:04,299 - __main__ - INFO - Epoch   7/10 | Train Loss: 1.9759 Acc: 0.3021 | Val Loss: 2.0809 Acc: 0.1250 | LR: 9.99e-05 | Time: 00:00
2025-06-26 17:07:04,457 - __main__ - INFO - Epoch   8/10 | Train Loss: 1.9602 Acc: 0.3021 | Val Loss: 2.0815 Acc: 0.1250 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:07:04,615 - __main__ - INFO - Epoch   9/10 | Train Loss: 1.9379 Acc: 0.3333 | Val Loss: 2.0823 Acc: 0.1250 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:07:04,774 - __main__ - INFO - Epoch  10/10 | Train Loss: 1.9196 Acc: 0.3229 | Val Loss: 2.0832 Acc: 0.1250 | LR: 9.98e-05 | Time: 00:00
2025-06-26 17:07:04,774 - __main__ - INFO - 分类器训练完成，用时: 00:01
2025-06-26 17:07:04,774 - __main__ - INFO - 最佳验证准确率: 0.1250
2025-06-26 17:07:04,774 - __main__ - INFO - 评估模型性能...
2025-06-26 17:07:05,820 - common.results_manager - INFO - 单次实验结果已保存: results\KAT\20250626_170105\individual_experiments\experiment_002
2025-06-26 17:07:05,820 - __main__ - INFO - 实验 2 完成
2025-06-26 17:07:05,832 - common.experiment_manager - INFO - 对比实验结果汇总已保存: results\KAT\20250626_170105\comparison_summary\comparison_results_summary.csv
2025-06-26 17:07:05,832 - common.experiment_manager - INFO - ================================================================================
2025-06-26 17:07:05,832 - common.experiment_manager - INFO - 对比实验结果汇总
2025-06-26 17:07:05,832 - common.experiment_manager - INFO - ================================================================================
2025-06-26 17:07:05,835 - common.experiment_manager - INFO - 
数据集: KAT
2025-06-26 17:07:05,835 - common.experiment_manager - INFO - ----------------------------------------
2025-06-26 17:07:05,835 - common.experiment_manager - INFO - 实验  1: 方法=CDDPM    | 样本=N/A | 生成=  5 | 准确率=0.0000 | 提升=+0.0000
2025-06-26 17:07:05,835 - common.experiment_manager - INFO - 实验  2: 方法=CDDPM    | 样本=N/A | 生成= 10 | 准确率=0.0000 | 提升=+0.0000
2025-06-26 17:07:05,846 - common.results_manager - INFO - 对比实验汇总已保存: results\KAT\20250626_170105\comparison_summary\comparison_summary.csv
2025-06-26 17:07:05,846 - __main__ - INFO - ================================================================================
2025-06-26 17:07:05,847 - __main__ - INFO - 🎉 智能重用对比实验全部完成，总用时: 06:00
2025-06-26 17:07:05,847 - __main__ - INFO - 💾 扩散模型重用次数: 1
2025-06-26 17:07:05,847 - __main__ - INFO - ⏱️ 预计节省时间: 约 0.0 倍扩散模型训练时间
2025-06-26 17:07:05,847 - __main__ - INFO - 结果保存在: results\KAT\20250626_170105
2025-06-26 17:07:05,847 - __main__ - INFO - 缓存配置保存在: cache\20250626_170105
2025-06-26 17:07:05,847 - __main__ - INFO - ================================================================================
2025-06-26 17:07:05,848 - __main__ - INFO - 实验完成
2025-06-26 17:07:05,848 - __main__ - INFO - 程序结束
