{"baseline_results": null, "augmented_results": {"total_epochs": 10, "best_val_accuracy": 0.25, "training_time": 0.6526908874511719, "train_losses": [2.084014654159546, 2.076814889907837, 2.065337657928467, 2.0559377670288086, 2.0619068145751953, 2.0451719760894775, 2.036893129348755, 2.0387444496154785, 2.027637243270874, 2.031348705291748], "val_losses": [2.079395055770874, 2.0797531604766846, 2.07993745803833, 2.0803256034851074, 2.0809292793273926, 2.0817575454711914, 2.0826566219329834, 2.0836527347564697, 2.084721565246582, 2.085753917694092], "train_accs": [0.125, 0.16666666666666666, 0.16666666666666666, 0.16666666666666666, 0.08333333333333333, 0.16666666666666666, 0.20833333333333334, 0.125, 0.16666666666666666, 0.16666666666666666], "val_accs": [0.125, 0.25, 0.125, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "baseline_metrics": {"accuracy": 0.13186813186813187, "precision": 0.02518861229268469, "recall": 0.13186813186813187, "f1_score": 0.038192325605606085}, "augmented_metrics": {"accuracy": 0.12487512487512488, "precision": 0.015593796812578033, "recall": 0.12487512487512488, "f1_score": 0.02772538296516982}, "improvement": {"accuracy": -0.006993006993006992, "precision": -0.009594815480106659, "recall": -0.006993006993006992, "f1_score": -0.010466942640436266}, "data_info": {"original_samples": 16, "generated_samples": 8, "total_samples": 24}, "diffusion_reused": true}